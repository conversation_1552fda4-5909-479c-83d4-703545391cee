# Keeps Indexer

O Keeps Indexer é responsável por consumir os eventos de tópicos do Kafka, montar documentos
com os dados atualizados e enviar para o Elasticsearch.

Este sistema trabalha em conjunto com o Keeps Connectors, que observa alterações no banco e popula
os eventos nos topicos correspondentes do Kafka.

# Estrutura do Projeto

- **App** - Entrypoint do sistema que inicia o **Worker** e registra os **Processors**.
- **Worker** - processo principal que conecta ao Kafka e distribui os eventos para os *Processors* registrados.
  - O worker identifica os tópicos que cada processor estão interessados, ao iniciar.
  - Entao, subscreve aos tópicos, agrupa os eventos em batches e distribui para os processadores correspondentes.
- **Processors** - implementações que consomem os eventos e monta documentos, conforme as regras de cada.
  - A intenção é ter um processador para cada índice diferente.
  - O contrato do processador está definido pela classe abstrata.
  - A implementacão LogProcessor exemplifica um processador que consome tópicos e imprime na saída padrão.


## Passos Executados


### Gitlab

- Configurar variaveis de CI
  - Settings > CI/CD > Variables > Expand > Add variables
    - `AWS_ACCESS_KEY_ID=[access key]`
    - `AWS_SECRET_ACCESS_KEY=[access secret]`
    - `AWS_REGION=us-east-1`
    - `ECR_REPOSITORY=[ecr url]`
- Configurar branches protegidas
  - Settings > Repository > Protected Branches > Expand > Protected a branch
    - `main`
    - `stage`
    - `production`

### AWS RDS

- Configurar novo Parameters group:
  - RDS > Parameters groups > Create parameter group
    - Family: `postgres13`
    - Group name: `replication_postgres13`
  - Parameter:
    - rds.logical_replication: `1`
    - SQL: `SHOW wal_level;` (deve ser `logical`)
- Usar usuario `postgres` (root) para replicação
    - Devido a limitação do RDS nao permitir criar usuários com permissão de `REPLICATION`.
    - Apesar desse usuário ser root, ele **NÃO** é `superuser` no RDS.
- Criar Publication
  - Criação Automática pelo Connector
    - Se usuário `postgres` for owner do database, as publications daquela base podem ser criadas automaticamente pelo Connector.
    - Verificar se o usuario `postgres` tem permissão de `CREATE` no database onde será criado as publications.
    - Verificar se o usuario `postgres` tem permissão de `SELECT` nas tabelas que serão monitaras pelas publications.
    - SQL: `GRANT CREATE ON DATABASE [database] TO postgres`;
    - SQL: `GRANT SELECT ON ALL TABLES IN SCHEMA public TO postgres;`;
  - Criação manual:
    - Conecte na base com owner da base ou alguém com privilégio de CREATE
    - Este mesmo usuário também deve ser owner das tabelas a serem replicadas
    - SQL: `GRANT CREATE ON DATABASE konquest_dev_db TO postgres;`
    - SQL: `CREATE PUBLICATION kafka_myaccount_publication FOR TABLE "user", user_role_workspace;`
    - SQL: `SELECT * FROM pg_catalog.pg_publication_tables;`

## AWS MSK

- Criar nova configuração de cluster
  - MSK > Cluster configuration > Create cluster configuration
    - Name: keeps-kafka-minimal
  - Parameters:
    - `default.replication.factor=2` (número de brokers)
    - `min.insync.replicas=1`
    - `auto.create.topics.enable=true`

- Criar novo cluster 
  - ... > Custom create
    - Cluster name: `kafka-learning-platform`
    - Version: `2.6.2 (recommended)`
    - Broker type: `kafka.t3.small`
    - Number of zones: `2` (minimo)
    - Number of brokers per zone: `1`
    - EBS storage volume per broker: `50 GiB`
    - Configuration: `Custom Configuration` -> `keeps-kafka-minimal`
  - Next...
    - VPC: `vpc-e7e87b82`
    - First/Second Zone/Subnet: `any`
    - Public access: `off`
    - Security groups: `sg-ce08eaaa` (default) 
  - Next...
    - Access control methods: `Unauthenticated Access`
    - ... enable PLAIN (TODO REVIEW)
  - Next...
    - Monitoring: `Basic`
  - Next...
    - Review 
  - Create cluster
    - Wait... 
    - ...just a little more...
    - ...almost there...
    - ...aaand... 
    - ...Done!
  - View client information
    - Copy TLS Private Endpoint


## AWS EB (Beanstalk) with Multi-Container

- Criar novo Application
  - EB > Application > Create new application
    - Application name: `kafka-connector-indexer`
    - Create

- Criar novo Environment
  - ... > Create new environment
    - Type: Web server environment
      - Environment name: `kafka-connector-indexer-[stage OU production]`
      - Domain: `keeps-kafka-connectors-[stage OU production]`.us-east-1.elasticbeanstalk.com
    - Managed platform
      - Platform: `Docker`
      - Platform branch: `Docker running on 64bits Amazon Linux 2`
      - Platform version: `3.4.11` (Recommended)
    - Clicar no botão `Configure more options`
    - Edit: Software
      - Environment properties:
        - `CONNECT_BOOTSTRAP_SERVERS=b-1.kafka-learning-platfor.7h2qni.c8.kafka.us-east-1.amazonaws.com:9092,b-2.kafka-learning-platfor.7h2qni.c8.kafka.us-east-1.amazonaws.com:9092`
        - `CONNECT_REST_ADVERTISED_HOST_NAME=keeps-kafka-connectors-[stage OU production].us-east-1.elasticbeanstalk.com`
        - `ELASTICSEARCH_SERVER=https://keeps.es.us-east-1.aws.found.io:9243`
        - `ELASTICSEARCH_USER=elastic`
        - `ELASTICSEARCH_PASSWORD=[password]`
        - `PG_HOSTNAME=keeps-learn-platform-us-east.cpd3dmaosiyq.us-east-1.rds.amazonaws.com`
        - `PG_PORT=5432`
        - `PG_USER=postgres`
        - `PG_PASSWORD=[password]`
        - `PG_DBNAME_MYACCOUNT=myaccount_[dev_]db`
        - `PG_DBNAME_KONQUEST=konquest_[dev_]db`
        - `PG_DBNAME_KONTENT=kontent_[dev_]db`
        - `PG_DBNAME_REGULATORY_COMPLIANCE=regulatory_compliance_[dev_]db`
      - Variáveis opcionais com os valores default
        - `ENV_SUFFIX=[stage OU production]` # definido no docker-compose
        - `INDEXER_INIT_INDEXES=true`
        - `INDEXER_INIT_INDEXES_ONLY=false`
        - `INDEXER_POLL_TIMEOUT=1`
        - `INDEXER_BATCH_MAX_SECONDS=60`
        - `INDEXER_BATCH_MAX_SIZE=100`
        - `INDEXER_LOG_MESSAGE=false`
        - `INDEXER_LOG_PAYLOAD=false`
        - `INDEXER_LOG_BATCH=false`
        - `TRAIL_LEARNING_OBJECT_TYPE_ID=d841e9d8-d669-4d88-9636-1072765d0738`
        - `MISSION_LEARNING_OBJECT_TYPE_ID=798e50d7-8b97-4979-8728-4f9f1599bb05`
    - Edit: Instances
      - EC2 security groups: `sg-ce08eaaa` (default)
    - Edit: Capacity
      - Instance types: `t3a.medium`
    - Clicar no botão `Create environment`


## Passos para atualização de connectors do Kafka Connect

- Caso precise reiniciar a importação do zero
  - Renomeie o connector 
    - Adicione um sufixo numerico no arquivo
    - Isso cria um nova entrada no Kafka 
  - Usar tombstones (?)
- Caso tenha trocado o nome do connector
  - Remover connector antigo da base do Connect
    - HTTP API com Basic Auth
    - `DELETE [kafka-connector-indexer-url]/connect/connectors/[connector-antigo]`
- Caso tenha alterado as tabelas do connector
  - Remover publication antiga
    - Postgres > [database do connector, stage OU production]
    - `DROP PUBLICATION [publication name];`
- Reinicie o Kafka Connect 
  - Fazer push dos connectors atualizados

## Passos para atualização de índices do Elasticsearch

- Pausar Indexer
  - Beanstalk > Configuration > Capacity > Time-based scaling
    - Add/Edit scheduled action
      - Name: `stop`
      - Min: `0`
      - Max: `0`
      - Desired: `0`
      - Ocurrence: `One-time`
      - Start time: `agora + 5 min`
    - Add > Apply
- Clonar Indices
  - Elastic > Management > Dev Tools > Console
    - Para cada indice a ser atualizado
    - `PUT /[nome do indice]/_settings` com body `{"settings": {"index.blocks.write": true} }`
    - `POST /[indice_original]/_clone/[indice_copia]`
- Remover Indices Originais
  - Elastic > Management > Stack Management > Data > Index Management
    - Selecione todos a serem atualizados
    - Manage `X` indices > Delete Indices > Review > Delete Indices
- Recriar Indices com Novo Mapping
  - Fazer push do Indexer com os mappings atualizados
  - Configurar Indexer para apenas inicializar os índices
    - Beanstalk > Configuration > Software > Environment properties
    - `INDEXER_INIT_INDEXES_ONLY=true`
  - Reiniciar Indexer
    - Beanlstak > Configuration > Capacity > Time-based scaling
    - Add/Edit scheduled action
      - Name: `start`
      - Min: `1`
      - Max: `1`
      - Desired: `1`
      - Ocurrence: `One-time`
      - Start time: `agora + 5 min`
    - Add/Save > Apply
- Restaurar conteúdo clonado com script de migração
  - Elastic > Management > Dev Tools > Console
    - `POST /_reindex` com body `{"source":..., "dest":..., "script":...}`
- Reativar indexação
  - Configurar Indexer para executar normalmente
    - Beanstalk > Configuration > Software > Environment properties
    - `INDEXER_INIT_INDEXES_ONLY=false` (ou remover item)
- Se OK, remover indices clonados
  - Elastic > Management > Stack Management > Data > Index Management
    - Selecione todos indices clonados
    - Manage `X` indices > Delete Indices > Review > Delete Indices


## Problemas encontrados

- Caso o connector já exista e foram alteradas as tabelas, apenas fazer DROP da publication não faz com que o connector a recrie.
  - Verificar se realmente isso acontece ou existe outra cusa
  - Solução atual: manualmente via `ALTER PUBLICATION`
- Mesmo problema acima, mas relacionado ao tópico no Kafka.


- Mesmo o usuário `postgres` sendo **root** ele pode falhar em criar publications em alguns casos, por causa de permissões.
  - Verificar permissoes para database, schema e tabelas.

## Comandos Úteis

### Project Setup

```bash
python -m venv .venv
source ./.venv/bin/activate
pip install -r requirements-dev.txt
pipdeptree


docker build . --tag indexer && docker run --rm --network kafka-connectors_default indexer


docker-compose up -d dev-elastic dev-kibana
docker-compose up --build kafka-data-indexer
```

### Manage Connectors ###
```bash

# List connectors
curl -s --user keeps-dev http://kafka-connector-indexer-stage.us-east-1.elasticbeanstalk.com/connect/connectors | jq

# Get connector status
curl -s --user keeps-dev http://kafka-connector-indexer-stage.us-east-1.elasticbeanstalk.com/connect/connectors/[connector] | jq

# Delete connector
curl -s --user keeps-dev -XDELETE http://kafka-connector-indexer-stage.us-east-1.elasticbeanstalk.com/connect/connectors/[connector] | jq
```

### Manage Postgres Publications
```sql

SHOW wal_level;

SHOW max_replication_slots;

-- Publications
SELECT * 
FROM pg_catalog.pg_publication_tables;

CREATE PUBLICATION [publication name]
FOR TABLE [
  "schema"."table",
  "public"."mission",
  ... ]; 


DROP PUBLICATION [publication name];

ALTER PUBLICATION [publication name]
ADD TABLE [new table];


-- Slots
SELECT * 
FROM pg_catalog.pg_replication_slots;

SELECT pg_drop_replication_slot('[slot name]');



-- Table Permissions
SELECT --*
	rtg.grantor, rtg.grantee, rtg.table_catalog, rtg.table_name, COUNT(rtg.privilege_type)
FROM information_schema.role_table_grants rtg 
--WHERE grantee = 'postgres'
GROUP BY 1, 2, 3, 4
ORDER BY 1, 2, 3, 4

-- Database Permissions
SELECT --*
	pd.datname , r.usename as grantor, e.usename as grantee, a.privilege_type
FROM pg_catalog.pg_database pd 
JOIN pg_catalog.pg_roles pr ON pd.datdba = pr."oid"
JOIN LATERAL (
  SELECT
    *
  from
    aclexplode(pd.datacl) as x
) a on true
join pg_user e on a.grantee = e.usesysid
join pg_user r on a.grantor = r.usesysid 
ORDER BY 1, 2, 3

-- Grants
GRANT CONNECT, CREATE ON DATABASE myaccount_db TO postgres;
GRANT USAGE, CREATE ON SCHEMA public TO postgres;
GRANT SELECT ON ALL TABLES TO postgres;

```


### Cloning Index ###
```bash
# Elasticsearch Console Dev

GET /kafka-analytics-activities/_settings

PUT /kafka-analytics-activities/_settings
{
  "settings": {
    "index.blocks.write": true
  }
}

POST /kafka-analytics-activities/_clone/kafka-analytics-activities_copy

POST _reindex
{
  "source": {
    "index": "kafka-analytics-activities_copy"
  },
  "dest": {
    "index": "kafka-analytics-activities"
  },
  "script": { 
    "source": """
      ctx._source.course_id = ctx._source.remove(\"mission_id\");
      ctx._source.workspaces = ctx._source.remove(\"owner_companies\"); 
      for (item in ctx._source.workspaces) { 
        item.workspace_id = item.remove(\"company_id\"); 
      }
    """
  }
}


```


### Problema de Instalação: `librdkafka/rdkafka.h: No such file or directory`

Durante a instalação do pacote `confluent-kafka`, você pode encontrar o seguinte erro:

```bash
fatal error: librdkafka/rdkafka.h: No such file or directory
```

Esse erro ocorre porque o pacote `confluent-kafka` depende da biblioteca `librdkafka`, que não está presente no sistema por padrão.

#### Solução

Para resolver esse problema, siga os passos abaixo para instalar a biblioteca `librdkafka` antes de instalar o pacote `confluent-kafka`.

1. **Instale a biblioteca `librdkafka` no seu sistema**:

   - Em sistemas **Debian/Ubuntu**, execute:
     ```bash
     sudo apt-get install librdkafka-dev
     ```

2. **Instale o pacote `confluent-kafka`**:
   Após a instalação da biblioteca `librdkafka`, instale o pacote `confluent-kafka` com o seguinte comando:
   ```bash
   pip install confluent-kafka
   ```

Seguindo essas etapas, o problema com a ausência de `rdkafka.h` deverá ser resolvido.


## Referências

* https://docs.confluent.io/platform/current/clients/confluent-kafka-python/html/index.html#pythonclient-configuration
* https://docs.confluent.io/platform/current/clients/confluent-kafka-python/html/index.html#pythonclient-consumer
* https://github.com/edenhill/librdkafka/blob/master/CONFIGURATION.md
* https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-put-mapping.html
* https://opensearch.org/docs/latest/clients/index/#legacy-clients

* https://garrett-jester.medium.com/build-a-real-time-backend-infrastructure-with-aws-msk-rds-ec2-and-debezium-1900c3ee5e67
* https://stackoverflow.com/questions/65409273/debezium-causes-postgres-to-run-out-of-disk-space-on-rds
* https://debezium.io/blog/2020/02/25/lessons-learned-running-debezium-with-postgresql-on-rds/
* https://groups.google.com/g/debezium/c/uyYqGWPTtdA
