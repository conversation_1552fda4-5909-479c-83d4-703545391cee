version: '3'

services: 

  # For local testing
  kafka-data-indexer:
    build: .
    depends_on:
      - dev-elastic
      # - dev-broker (using kafka_connectors network)
    environment:
      ENV_SUFFIX: 'dev'
      ELASTICSEARCH_SERVER: dev-elastic:9200
      ELASTICSEARCH_PASSWORD: ""
      ELAST<PERSON><PERSON>ARCH_USER: ""
      KAFKA_SERVERS: dev-broker:29092
      INDEXER_POLL_TIMEOUT: 5
      INDEXER_BATCH_MAX_SECONDS: 15
      INDEXER_BATCH_MAX_SIZE: 10
      INDEXER_LOG_MESSAGE: 'true'
      INDEXER_LOG_PAYLOAD: 'true'
      INDEXER_LOG_BATCH: 'true'
      DATABASE_DEV_POSTGRES_URL: 'postgresql://dev-postgres'

  keeps-smartzap-sync:
    build: .
    command: "python -u ./app-smartzap-data-sync.py"
    environment:
      ENV_SUFFIX: 'dev'
      ELASTIC<PERSON>ARCH_SERVER: dev-elastic:9200
      ELASTICSEARCH_PASSWORD: ""
      ELASTICSEARCH_USER: ""
      KAFKA_SERVERS: dev-broker:29092
      INDEXER_POLL_TIMEOUT: 5
      INDEXER_BATCH_MAX_SECONDS: 15
      INDEXER_BATCH_MAX_SIZE: 10
      INDEXER_LOG_MESSAGE: 'true'
      INDEXER_LOG_PAYLOAD: 'true'
      INDEXER_LOG_BATCH: 'true'
      DATABASE_DEV_POSTGRES_URL: 'postgresql://dev-postgres'
      TRAIL_LEARNING_OBJECT_TYPE_ID: "d841e9d8-d669-4d88-9636-1072765d0738"
      MISSION_LEARNING_OBJECT_TYPE_ID: "798e50d7-8b97-4979-8728-4f9f1599bb05"
      PG_HOSTNAME: host.docker.internal
      PG_PORT: "5432"
      PG_USER: postgres
      PG_PASSWORD: 1234
      PG_DBNAME_MYACCOUNT: myaccount_db
      PG_DBNAME_KONQUEST: konquest_dev_db
      PG_DBNAME_KONTENT: kontent_dev_db
      PG_DBNAME_SMARTZAP: smartzap_db
      PG_DBNAME_NOTIFICATION: notification_dev_db
      PG_DBNAME_REGULATORY_COMPLIANCE: regulatory_compliance_dev_db
      PG_DBNAME_INTEGRATION_GATEWAY_ALURA: integration_gateway_alura_dev_db


  dev-elastic:
    image: elasticsearch:7.14.2
    ports:
      - 9200:9200
      - 9300:9300
    environment:
      discovery.type: single-node

  dev-kibana:
    image: kibana:7.14.2
    ports:
      - 5601:5601
    depends_on:
      - dev-elastic
    environment:
      ELASTICSEARCH_HOSTS: http://dev-elastic:9200

networks:
  default:
    external: true
    name: kafka-connectors_net
