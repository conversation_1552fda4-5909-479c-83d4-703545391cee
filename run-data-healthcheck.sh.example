#!/bin/bash

export ELASTICSEARCH_SERVER=https://keeps.es.us-east-1.aws.found.io
export ELASTICSEARCH_PASSWORD=...
export PG_HOSTNAME=rds-postgres-stage.cpd3dmaosiyq.us-east-1.rds.amazonaws.com
export PG_PORT=5432
export PG_USER=postgres
export PG_PASSWORD=...

# STAGE
export ENV_SUFFIX=stage
export PG_DBNAME_MYACCOUNT=myaccount_dev_db
export PG_DBNAME_KONQUEST=konquest_dev_db
export PG_DBNAME_KONTENT=kontent_dev_db


python data-healthcheck.py
