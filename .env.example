ENV_SUFFIX='dev'
ELASTICSEARCH_SERVER=http://elastic:9200
ELASTICSEARCH_PASSWORD=senhaSuperSegura
ELASTICSEARCH_USER=elastic
KAFKA_SERVERS=kafka:9092
INDEXER_POLL_TIMEOUT=5
INDEXER_BATCH_MAX_SECONDS=15
INDEXER_BATCH_MAX_SIZE=10
INDEXER_LOG_MESSAGE='true'
INDEXER_LOG_PAYLOAD='true'
INDEXER_LOG_BATCH='true'
DATABASE_DEV_POSTGRES_URL='postgresql://dev-postgres'
TRAIL_LEARNING_OBJECT_TYPE_ID="d841e9d8-d669-4d88-9636-1072765d0738"
MISSION_LEARNING_OBJECT_TYPE_ID="798e50d7-8b97-4979-8728-4f9f1599bb05"
PG_HOSTNAME=host.docker.internal
PG_PORT="5432"
PG_USER=postgres
PG_PASSWORD=123456
PG_DBNAME_MYACCOUNT=myaccount_dev_db
PG_DBNAME_KONQUEST=konquest_dev_db
PG_DBNAME_KONTENT=kontent_dev_db
PG_DBNAME_SMARTZAP=smartzap_dev_db
PG_DBNAME_NOTIFICATION=notification_dev_db
PG_DBNAME_REGULATORY_COMPLIANCE=regulatory_compliance_dev_db
PG_DBNAME_INTEGRATION_GATEWAY_ALURA=integration_gateway_alura_dev_db