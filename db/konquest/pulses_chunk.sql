SELECT
    p.id,
    p."name",
    p.description,
    p."language",
    p.points,
    p.status,
    p.duration_time,
    p.created_date,
    p.updated_date,
    p.pulse_type_id,
    pt."name" AS pulse_type_name,
    p.user_creator_id,
    u.name AS user_creator_name,
    u.avatar AS user_creator_avatar,
    ARRAY(
        SELECT c.workspace_id
        FROM pulse_channel pc
        JOIN channel c ON pc.channel_id = c.id
        WHERE pc.pulse_id = p.id
    ) AS workspace_id,
    p.holder_image,
    p.learn_content_uuid as learn_content_id,
    p.is_active
FROM
    pulse p
JOIN
    pulse_type pt ON p.pulse_type_id = pt.id
JOIN
    "user" u ON p.user_creator_id = u.id
WHERE
    p.id IN ($pulses_ids)
    AND p.deleted IS FALSE
