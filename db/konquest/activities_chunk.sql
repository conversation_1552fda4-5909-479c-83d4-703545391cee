SELECT
   lca.id,
   lca.user_id,
   lca.workspace_id,
   lca.pulse_id,
   lca.mission_enrollment_id,
   lca.mission_stage_content_id,
   msc.stage_id,
   ms.mission_id,
   COALESCE(msc.learn_content_uuid, p.learn_content_uuid) AS kontent_id,
   lca.action,
   lca.time_start,
   lca.time_stop,
   lca.time_in,
   lca.created_date
FROM
   learn_content_activity lca
LEFT JOIN
   pulse p ON p.id = lca.pulse_id
LEFT JOIN
   mission_stage_content msc ON msc.id = lca.mission_stage_content_id
LEFT JOIN
   mission_stage ms ON ms.id = msc.stage_id
WHERE
   lca.id IN ($activities_ids)
   AND lca.deleted IS FALSE
