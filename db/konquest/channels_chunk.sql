SELECT
    c.id,
    c.name,
    c.description,
    c.created_date,
    c.updated_date,
    c.is_active,
    c.language,
    c.user_creator_id,
    u.name AS user_creator_name,
    u.avatar AS user_creator_avatar,
    c.workspace_id,
    c.channel_type_id,
    ct.name AS channel_type_name,
    c.channel_category_id,
    cc.name AS channel_category_name,
    c.holder_image
FROM
    channel c
JOIN
    channel_type ct ON c.channel_type_id = ct.id
JOIN
    channel_category cc ON c.channel_category_id = cc.id
JOIN
    "user" u ON c.user_creator_id = u.id
WHERE
   c.id IN ($channels_ids)
   AND c.deleted IS FALSE
