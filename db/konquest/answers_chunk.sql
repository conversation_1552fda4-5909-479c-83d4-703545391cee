SELECT
    a.id,
    a.is_ok,
    a.user_id,
    q.id AS question_id,
    q.points AS question_points,
    q.exam_question AS question_name,
    e.id AS exam_id,
    e.title AS exam_name,
    e.pulse_id,
    e.channel_id,
    e.stage_id,
    ms.mission_id,
    a.enrollment_id,
    a.created_date,
    a.updated_date
FROM
    answer a
JOIN
    question q ON a.exam_has_question_id = q.id
JOIN
    exam e ON q.exam_id = e.id
LEFT JOIN 
    mission_stage ms ON ms.id = e.stage_id
WHERE
    a.id IN ($answers_ids)
    AND a.deleted IS FALSE
