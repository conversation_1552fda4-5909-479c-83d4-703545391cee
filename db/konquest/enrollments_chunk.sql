SELECT
    me.id,
    me.user_id,
    me.workspace_id,
    me.mission_id,
    m.name AS mission_name,
    mc.id AS category_id, 
    mc.name AS category_name,
    u.name AS user_name,
    u.avatar AS user_avatar,
    me.status,
    me.points,
    me.performance,
    me.progress,
    me.give_up,
    me.give_up_comment,
    me.required,
    me.goal_date,
    me.created_date,
    me.start_date,
    me.end_date,
    me.updated_date,
    (
        SELECT COUNT(*)
        FROM question q
        JOIN mission_stage_content msc ON q.exam_id = msc.learn_content_uuid
            AND msc.content_type = 'EXAM'
        JOIN mission_stage ms ON msc.stage_id = ms.id
        WHERE ms.mission_id = me.mission_id
    ) AS questions_total,
    (
        SELECT COUNT(*)
        FROM answer a
        WHERE a.enrollment_id = me.id
    ) AS questions_answered,
    (
        SELECT COUNT(*)
        FROM answer a
        WHERE a.enrollment_id = me.id
            AND a.is_ok = TRUE
    ) AS questions_answered_correctly
FROM
    mission_enrollment me
JOIN
    mission m on m.id = me.mission_id
JOIN
    mission_category mc on mc.id = m.mission_category_id
JOIN
    "user" u ON u.id = me.user_id
WHERE
    me.id IN ($enrollments_ids)
    AND me.deleted IS FALSE
