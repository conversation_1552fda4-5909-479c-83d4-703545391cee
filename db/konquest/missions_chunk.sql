SELECT
    mi.id,
    mi.id as mission_id,
    mi.name,
    mi.description,
    mi.duration_time,
    mi.points,
    mi.created_date,
    mi.updated_date,
    mi.mission_category_id,
    mi.mission_type_id,
    mi.user_creator_id,
    mi.is_active,
    mi.development_status,
    mi.thumb_image,
    mi.holder_image,
    mi.vertical_holder_image,
    mi.summary,
    mi.language,
    mi.expiration_date,
    mi.mission_model,
    mi.required_evaluation,
    mi.assessment_type,
    mi.allow_self_enrollment_renewal,
    mi.allow_self_reproved_enrollment_renewal,
    mi.minimum_performance,
    u.name AS user_creator_name,
    u.status AS user_creator_status,
    u.email AS user_creator_email,
    u.avatar AS user_creator_avatar,
    mc.name AS mission_category_name,
    mt.name AS mission_type_name,
    em.id AS mission_external_id,
    em.course_type AS mission_external_course_type,
    em.course_url AS mission_external_course_url,
    mp.id AS mission_external_provider_id,
    mp.name AS mission_external_provider_name,
    mp.description AS mission_external_provider_description,
    mp.icon AS mission_external_provider_icon,
    pm.id AS mission_presential_id,
    pm.address AS mission_presential_address,
    pm.seats AS mission_presential_seats,
    pm.remaining_seats AS mission_presential_remaining_seats,
    pm.next_date AS mission_presential_next_date,
    pm.notify_users_enrolled AS mission_presential_notify_users_enrolled,
    pm.allow_any_enrollment AS mission_presential_allow_any_enrollment,
    lm.id AS mission_live_id,
    lm.url AS mission_live_url,
    lm.seats AS mission_live_seats,
    lm.remaining_seats AS mission_live_remaining_seats,
    lm.next_date AS mission_live_next_date,
    lm.notify_users_enrolled AS mission_live_notify_users_enrolled,
    lm.allow_any_enrollment AS mission_live_allow_any_enrollment
FROM
    mission mi
JOIN
    "user" u ON u.id = mi.user_creator_id
LEFT JOIN
    mission_category mc ON mc.id = mi.mission_category_id
LEFT JOIN
    mission_type mt ON mt.id = mi.mission_type_id
LEFT JOIN 
    external_mission em ON em.mission_id = mi.id
LEFT JOIN
    mission_provider mp ON mp.id = em.provider_id
LEFT JOIN
    presential_mission pm ON pm.mission_id = mi.id
LEFT JOIN
    live_mission lm ON lm.mission_id = mi.id
WHERE
    mi.id IN ($missions_ids)
    AND mi.deleted IS FALSE
