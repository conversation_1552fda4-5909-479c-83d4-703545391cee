SELECT
    g.id,
    g.id AS group_id,
    g."name",
    g.created_date,
    g.updated_date,
    g.workspace_id,
    ARRAY(SELECT gm.mission_id FROM group_mission gm WHERE gm.group_id = g.id AND gm.deleted IS FALSE) AS missions,
    ARRAY(SELECT gc.channel_id FROM group_channel gc WHERE gc.group_id = g.id AND gc.deleted IS FALSE) AS channels,
    ARRAY(SELECT glt.learning_trail_id FROM group_learning_trail glt WHERE glt.group_id = g.id AND glt.deleted IS FALSE) AS learning_trails,
    ARRAY(SELECT gu.user_id FROM group_user gu WHERE gu.group_id = g.id AND gu.deleted IS FALSE) AS users
FROM "group" g
WHERE
    g.id IN ($groups_ids)
    AND g.deleted IS FALSE
