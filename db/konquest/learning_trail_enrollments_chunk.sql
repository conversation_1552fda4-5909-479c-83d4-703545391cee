SELECT
    lte.id,
    lte.status,
    lte.points,
    lte.performance,
    lte.progress,
    lte.give_up,
    lte.give_up_comment,
    lte.required,
    lte.start_date,
    lte.end_date,
    lte.goal_date,
    lte.created_date,
    lte.updated_date,
    lte.workspace_id,
    lte.user_id,
    u.name AS user_name,
    lte.learning_trail_id,
    lt.name AS learning_trail_name
FROM
    learning_trail_enrollment lte
JOIN
    learning_trail lt on lt.id = lte.learning_trail_id
JOIN
    "user" u ON u.id = lte.user_id
WHERE
    lte.id IN ($enrollments_ids)
    AND lte.deleted IS FALSE
