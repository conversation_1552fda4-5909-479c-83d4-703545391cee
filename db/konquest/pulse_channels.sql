SELECT
    pc.id,
    pc.pulse_id,
    pc.channel_id,
    c."name" as channel_name,
    c.is_active as channel_is_active,
    c.description as channel_description,
    c.workspace_id,
    c.channel_type_id,
    ct."name" AS channel_type_name,
    c.channel_category_id,
    cc."name" AS channel_category_name
FROM
    pulse_channel pc
JOIN
    channel c ON pc.channel_id = c.id
JOIN
    channel_type ct ON c.channel_type_id = ct.id
JOIN
    channel_category cc ON c.channel_category_id = cc.id
WHERE
    pc.deleted IS FALSE
    AND pc.pulse_id = '$pulse_id'
