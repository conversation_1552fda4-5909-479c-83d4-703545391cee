SELECT
   m.id AS mission_id,
   m.name AS mission_name,
   ms.id AS stage_id,
   ms.name AS stage_name,
   ms.order AS stage_order,
   msc.id AS content_id,
   msc.name AS content_name,
   msc.order AS content_order,
   msc.content_type AS stage_content_type,
   msc.learn_content_uuid AS kontent_content_id
FROM
   mission_stage_content msc
JOIN mission_stage ms 
   ON ms.id = msc.stage_id
JOIN mission m
   ON m.id = ms.mission_id
WHERE
   m.id = '$mission_id'
   AND msc.deleted IS FALSE
ORDER BY
   m.id, ms.order, msc.order
