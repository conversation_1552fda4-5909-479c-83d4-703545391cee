SELECT
    lt.id,
    lt.id AS learning_trail_id,
    lt."name",
    lt.description,
    lt.duration_time,
    lt.points,
    lt.is_active,
    lt.thumb_image,
    lt.holder_image,
    lt.language,
    lt.created_date,
    lt.updated_date,
    lt.expiration_date,
    lt.learning_trail_type_id,
    ltt."name" AS learning_trail_type_name,
    lt.user_creator_id,
    u."name" AS user_creator_name,
    u.avatar AS user_creator_avatar
FROM 
    learning_trail lt
JOIN 
    learning_trail_type ltt ON lt.learning_trail_type_id = ltt.id
JOIN 
    "user" u ON lt.user_creator_id = u.id 
WHERE
    lt.id IN ($ids)
    AND lt.deleted IS FALSE
