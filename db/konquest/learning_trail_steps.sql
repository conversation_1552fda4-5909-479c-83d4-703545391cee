SELECT
    lts.id,
    lts.id AS step_id,
    lts."order" AS step_order,
    lts.learning_trail_id,
    lts.mission_id,
    m.name AS mission_name,
    mc.id AS mission_category_id,
    mc."name" AS mission_category_name,
    lts.pulse_id,
    p."name" AS pulse_name
FROM 
    learning_trail_step lts
LEFT JOIN
    mission m ON lts.mission_id = m.id
LEFT JOIN
    mission_category mc ON m.mission_category_id = mc.id
LEFT JOIN
    pulse p ON lts.pulse_id = p.id 
WHERE
    lts.learning_trail_id = '$learning_trail_id'
    AND lts.deleted IS FALSE
ORDER BY 
    lts.learning_trail_id, lts."order"
