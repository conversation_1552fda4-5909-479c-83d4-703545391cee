# Database engines and queries
import os
import uuid
from string import Template

from sqlalchemy import text
from sqlalchemy.engine import Connection, ResultProxy


# Export all engines
from .konquest import engine as konquest_engine  # noqa: unused-import
from .kontent import engine as kontent_engine  # noqa: disable=unused-import
from .myaccount import engine as myaccount_engine  # noqa: disable=unused-import
from .smartzap import engine as smartzap_engine  # noqa: disable=unused-import
from .regulatory_compliance import engine as regulatory_compliance  # noqa: disable=unused-import
from .dev_postgres import engine as dev_postgres_engine  # noqa: disable=unused-import


def format_query_values(values: dict):
    for arg_key in values:
        value = values[arg_key]
        if isinstance(value, list):
            value = value if len(value) > 0 else [uuid.uuid4()]
            values[arg_key] = ','.join(f"'{item}'" for item in value)
        elif value is None:
            values[arg_key] = "NULL"
        elif isinstance(value, str):
            value = value.replace("'", "''")
            values[arg_key] = f"'{value}'"

    return values


# Run a sql file
def run_sql(connection: Connection, file_path: str, **kwargs) -> ResultProxy:
    """
    Run SQL statements from a template, replacing any variables specified in kwargs.

    :param connection: Database connection
    :param file_path: Location of the SQL file
    :param kwargs: any combination of key=value replaced in the SQL file

    :return: SQL execution response
    """
    file_message = f'{os.path.dirname(__file__)}/{file_path}'
    sql = Template(open(file_message, mode="r", encoding='utf-8').read()).substitute(kwargs)
    rs = connection.execute(text(sql))
    return rs
