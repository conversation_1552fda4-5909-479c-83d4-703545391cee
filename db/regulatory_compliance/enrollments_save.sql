INSERT INTO enrollment (
  id, created_date, updated_date, learning_object_id, user_id,
  status, goal_date, start_date, end_date,
  workspace_id, deleted_date
)
VALUES
  (
   $id,
   $created_date,
   $updated_date,
   (
        SELECT
            id
        FROM
            learning_object
        WHERE
            source_id=$learning_object_source_id
   ),
   $user_id,
   $status,
   $goal_date,
   $start_date,
   $end_date,
   $workspace_id,
   $deleted_date
  ) ON CONFLICT(id) DO
UPDATE
SET
  created_date = $created_date,
  updated_date = $updated_date,
  learning_object_id = (
        SELECT
            id
        FROM
            learning_object
        WHERE
            source_id=$learning_object_source_id
  ),
  user_id = $user_id,
  status = $status,
  goal_date = $goal_date,
  start_date = $start_date,
  end_date = $end_date,
  workspace_id = $workspace_id,
  deleted_date = $deleted_date
