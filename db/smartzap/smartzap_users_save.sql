INSERT INTO "user" (
  id, name, phone, email, cpf, ein, birthday, tags, created, updated, my_account_user, avatar, sync_check,
  director, manager, area_of_activity, job
)
VALUES (
  $id, $name, $phone, $email, $cpf, $ein, $birthday, NULL, $created, CURRENT_TIMESTAMP, true, $avatar, $sync_check,
  $director, $manager, $area_of_activity, $job
)
ON CONFLICT (id) DO
UPDATE
SET
  name = $name,
  phone = $phone,
  email = $email,
  cpf = $cpf,
  ein = $ein,
  birthday = $birthday,
  tags = NULL,
  updated = CURRENT_TIMESTAMP,
  avatar = $avatar,
  sync_check = $sync_check,
  director = $director,
  manager = $manager,
  area_of_activity = $area_of_activity,
  job = $job;
