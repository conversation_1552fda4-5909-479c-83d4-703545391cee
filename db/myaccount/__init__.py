import os
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker

DB_NAME = os.getenv('PG_DBNAME_MYACCOUNT')
DB_HOST = os.getenv('PG_HOSTNAME')
DB_PORT = os.getenv('PG_PORT', 5432)
DB_USER = os.getenv('PG_USER')
DB_PASS = os.getenv('PG_PASSWORD')
DB_URL = f'postgresql://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}'

engine = create_engine(DB_URL, convert_unicode=True)
session = scoped_session(sessionmaker(autocommit=False, autoflush=True, bind=engine))
