SELECT
    urw.id,
    urw.user_id,
    urw.workspace_id,
    urw.role_id,
    r.application_id,
    r."name" AS role_name,
    r.key as role_key,
    urw.created_date,
    urw.updated_date
FROM
    user_role_workspace urw
JOIN
    "role" r on r.id=urw.role_id
JOIN
    service_workspace sw on sw.workspace_id = urw.workspace_id
WHERE
    urw.user_id = '$user_id' AND r.application_id = '$smartzap_application_id' AND sw.service_id = '$smartzap_service_id'
