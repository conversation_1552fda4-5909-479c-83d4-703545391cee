SELECT
    u.id,
    u.name,
    u.nickname,
    u.email,
    u.secondary_email,
    u.phone,
    u.gender,
    u.birthday,
    u.address,
    u.avatar,
    u.status,
    u.created_date,
    u.updated_date,
    u.language_id,
    leader.id AS leader_id,
    leader."name" AS leader_name
FROM 
    "user" u
LEFT JOIN "user" leader
    ON u.related_user_leader_id = leader.id
WHERE
    u.id IN ($users_ids)
