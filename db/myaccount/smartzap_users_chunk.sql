SELECT
    DISTINCT(u.id),
    u.name,
    u.nickname,
    u.email,
    u.secondary_email,
    u.cpf,
    u.ein,
    u.phone,
    u.gender,
    u.birthday,
    u.address,
    u.avatar,
    u.status,
    u.created_date,
    u.updated_date,
    u.language_id,
    <PERSON><PERSON>(upw.director) AS director,
    <PERSON><PERSON>(upw.manager) AS manager,
    MAX(upw.area_of_activity) AS area_of_activity,
    MAX(j.name) AS "job"
FROM
    "user" u
JOIN
    user_role_workspace urw ON urw.user_id = u.id
LEFT JOIN
    user_profile_workspace upw ON upw.user_id = u.id AND upw.workspace_id = urw.workspace_id
LEFT JOIN
    job j ON j.id = upw.job_position_id
JOIN
    "role" r ON r.id = urw.role_id
WHERE
    u.id IN ($users_ids) AND r.application_id = '$smartzap_application_id'
GROUP BY
    u.id