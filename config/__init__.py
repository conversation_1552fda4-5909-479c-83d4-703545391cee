# Proposição para config modular compartilhado

# TODO:
# - verificar melhor forma de centralizar configuração
# - substituir uso das variáveis do ambiente por serviço centralizado

import os


def _get_int_or_default(key: str, default: int) -> int:
    try:
        return int(os.getenv(key, default))
    except Exception:
        return default


def _get_bool_or_default(key: str, default: bool) -> bool:
    value = os.getenv(key, '')
    if value.lower() in ['true', 'on', 'yes', '1']:
        return True
    if value.lower() in ['false', 'off', 'no', '0']:
        return False
    return default


def _get_str_or_default(key: str, default: str) -> str:
    return os.getenv(key, default)


# Configuration service
class Config:
    POLL_TIMEOUT = _get_int_or_default('INDEXER_POLL_TIMEOUT', 1)
    BATCH_MAX_SECONDS = _get_int_or_default('INDEXER_BATCH_MAX_SECONDS', 60)
    BATCH_MAX_SIZE = _get_int_or_default('INDEXER_BATCH_MAX_SIZE', 100)

    LOG_MESSAGE = _get_bool_or_default('INDEXER_LOG_MESSAGE', False)
    LOG_PAYLOAD = _get_bool_or_default('INDEXER_LOG_PAYLOAD', False)
    LOG_BATCH = _get_bool_or_default('INDEXER_LOG_BATCH', False)

    INIT_INDEXES = _get_bool_or_default('INDEXER_INIT_INDEXES', True)
    INIT_INDEXES_ONLY = _get_bool_or_default('INDEXER_INIT_INDEXES_ONLY', False)
