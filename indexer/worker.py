import os
import typing  # python 3.8<
import datetime
import json
import confluent_kafka as kafka
import elasticapm
from elasticsearch.helpers import BulkIndexError

from es import create_or_update_index
from .processors import KeepsMessage, AbstractProcessor
from .logging_config import get_logger


POLL_TIMEOUT = os.getenv("INDEXER_POLL_TIMEOUT", "1")  # noqa
BATCH_MAX_SECONDS = os.getenv("INDEXER_BATCH_MAX_SECONDS", "60")  # noqa
BATCH_MAX_SIZE = os.getenv("INDEXER_BATCH_MAX_SIZE", "100")  # noqa

LOG_MESSAGE = os.getenv("INDEXER_LOG_MESSAGE", "False")  # noqa
LOG_PAYLOAD = os.getenv("INDEXER_LOG_PAYLOAD", "False")  # noqa
LOG_BATCH = os.getenv("INDEXER_LOG_BATCH", "False")  # noqa

INIT_INDEXES = os.getenv("INDEXER_INIT_INDEXES", "True")  # noqa
INIT_INDEXES_ONLY = os.getenv("INDEXER_INIT_INDEXES_ONLY", "False")  # noqa

# TODO doesn't work. Use Tombstones?
RESET_TOPICS = os.getenv("RESET_TOPICS", None)  # noqa


class _ProcessorData:
    processor: AbstractProcessor
    batch: typing.List[KeepsMessage]

    def __init__(self, processor: AbstractProcessor):
        self.processor = processor
        self.batch = []

    def __str__(self) -> str:
        return self.processor.__class__.__name__


class IndexerWorker:
    config: dict
    consumer: kafka.Consumer
    poll_timeout: int
    batch_max_seconds: int
    batch_max_size: int

    running = False
    total = 0

    registeredProcessors: typing.List[_ProcessorData] = []
    topicToProcessors: typing.Dict[str, typing.List[_ProcessorData]] = {}

    _topics: typing.List[str]
    _batch_msg_count: int
    _batch_timeout_count: int

    apm_client: elasticapm.Client

    def __init__(
        self,
        config: dict,
        apm_client: elasticapm.Client = None,
        poll_timeout=int(POLL_TIMEOUT),
        batch_max_seconds=int(BATCH_MAX_SECONDS),
        batch_max_size=int(BATCH_MAX_SIZE),
    ):
        config["on_commit"] = self._log_async_commit
        config["max.poll.interval.ms"] = 1800000
        self.config = config
        self.consumer = kafka.Consumer(config)
        self.poll_timeout = poll_timeout
        self.batch_max_seconds = batch_max_seconds
        self.batch_max_size = batch_max_size
        self.logger = get_logger("IndexerWorker")
        self._log_especial_values()

        self.apm_client = apm_client

    def addProcessor(self, processor: AbstractProcessor):
        processorData = _ProcessorData(processor)
        self.registeredProcessors.append(processorData)

        topics = processor.get_kafka_topics()
        for topic in topics:
            if topic not in self.topicToProcessors:
                self.topicToProcessors[topic] = []
            self.topicToProcessors[topic].append(processorData)

        self._log(f'[#{len(self.registeredProcessors)}] Registered "{processorData}" for topics: {topics}')

    def start(self):
        # Mostrar banner de inicialização
        self._show_startup_banner()

        if not self.topicToProcessors:
            self._log("No topics registered. Terminating...")
            return

        if INIT_INDEXES in ["1", "true", "True"] or INIT_INDEXES_ONLY in ["1", "true", "True"]:
            self._initialize_indexes()

        self.running = True
        self._topics = []
        self._batch_msg_count = 0
        self._batch_timeout_count = 0

        try:

            if INIT_INDEXES_ONLY in ["1", "true", "True"]:
                self._log("Init indexes only flag active. Terminating...")
                return

            self.logger.info("Listening to topics:")
            for topic in self.topicToProcessors.keys():
                self._topics.append(topic)
                self.logger.info(f"  • {topic}")
            self.logger.success("✅ Started successfully!")

            self.consumer.subscribe(self._topics, on_assign=self._on_assign_callback)

            while self.running:
                msg = self.consumer.poll(timeout=self.poll_timeout)

                if msg is None:
                    self._batch_timeout_count += 1

                    countdown = self.batch_max_seconds / self.poll_timeout - self._batch_timeout_count
                    # self._log('_batch_timeout_count: {self._batch_timeout_count}')
                    if LOG_BATCH and (countdown < 5 or countdown % 10 == 0):
                        self._log(f"...batch timeout countdown: {int(countdown)}")
                    if countdown <= 0:
                        self._process_batch()

                    continue

                if msg.error():
                    e = f"[ERROR] Message: {msg.error()}"
                    self._log(e)
                    self.apm_client.capture_message(e)
                    continue

                topic = msg.topic()
                if topic not in self.topicToProcessors:
                    e = f"[WARNING] No processor defined for topic: {topic}"
                    self._log(e)
                    self.apm_client.capture_message(e)
                    continue

                self.total += 1
                kmsg = KeepsMessage(msg)
                self._log_message(kmsg)

                [processorData.batch.append(kmsg) for processorData in self.topicToProcessors[topic]]

                self._batch_msg_count += 1
                if self._batch_msg_count >= self.batch_max_size:
                    self._process_batch()

        except BulkIndexError as e:
            self.logger.error(f"An error occurred when inserting batch messages: {e}")
            self.logger.error(json.dumps(e.errors, indent=2))
            raise e

        finally:
            self.logger.warning("🛑 Shutting down...")
            self.consumer.close()
            self.running = False
            self.logger.info("✅ Stopped gracefully")

    def _initialize_indexes(self):
        self._log("*** Initializing indexes...")
        for data in self.registeredProcessors:
            index = data.processor.get_index_name()
            if index:
                settings = data.processor.get_index_settings()
                mappings = data.processor.get_index_mappings()
                create_or_update_index(index=index, mappings=mappings, settings=settings)

    def _process_batch(self):
        self._batch_timeout_count = 0
        if not self._batch_msg_count:
            if LOG_BATCH:
                self._log("...empty batch... nothing to do...")
            return

        self.apm_client.begin_transaction(transaction_type="batch_processing")
        start_batch = datetime.datetime.now()
        self._log(f"*** Processing pending batches. Total messages: {self._batch_msg_count}")

        processed_count = 0
        for processorData in self.registeredProcessors:
            if not processorData.batch:
                continue

            with elasticapm.capture_span(f"processor: {str(processorData)}"):
                start_processor = datetime.datetime.now()
                self._log(f"-- Starting {processorData} for {len(processorData.batch)} messages")

                ok = processorData.processor.pre_process_batch(processorData.batch)
                if ok:
                    self._log(f"[{processorData}] OK for batch: {len(processorData.batch)}")
                else:
                    self._log(f"[{processorData}] NOT OK for batch: {len(processorData.batch)}")
                    # TODO retry?

                duration = (datetime.datetime.now() - start_processor).total_seconds()
                self._log(f"-- Ending {processorData} (started at {start_processor}, took {divmod(duration, 60)[0]} minutes)")

                processed_count += len(processorData.batch)
                processorData.batch = []

        duration = (datetime.datetime.now() - start_batch).total_seconds()
        self._log(f"*** Ending batches (started at {start_batch}, took {divmod(duration, 60)[0]} minutes)")
        self._log(f"*** Total processed from last batches: {processed_count}")
        self._log("")

        self._batch_msg_count = 0
        self.consumer.commit(asynchronous=False)
        self.apm_client.end_transaction(name=__name__, result="success")

    def _on_assign_callback(self, consumer, partitions):
        consumer.assign(partitions)

        self._log("*** Topics assignments:")
        for p in partitions:
            low_mark, high_mark = consumer.get_watermark_offsets(p)
            self._log(f" - {p.topic} : {p.offset} / low: {low_mark} / high: {high_mark}")

        if RESET_TOPICS:  # TODO doesn't work. Use Tombstones?
            topicsToReset = [t.strip() for t in RESET_TOPICS.split(",")]
            self._log("*** Reseting topics:")

            for p in partitions:
                if p.topic in topicsToReset:
                    low_mark, high_mark = consumer.get_watermark_offsets(p)
                    self._log(f" - {p.topic} / low: {low_mark} / high: {high_mark}")
                    p.offset = 0  # kafka.OFFSET_BEGINNING
                    consumer.seek(p)

            self._log("")

    def _log_async_commit(self, err, partitions):
        if err:
            self._log(str(err))
        else:
            self._log("*** Committed partition offsets:")
            for partition in partitions:
                self._log(f"- {partition}")
        self._log("")

    def _log_message(self, kmsg: KeepsMessage):
        if LOG_MESSAGE not in ["1", "true", "True"]:
            return

        includePayload = LOG_PAYLOAD in ["1", "true", "True"]

        self._log("----------------------------------")
        self._log(f"*** message reveived #{self.total}")
        kmsg.log(includePayload)
        self._log("")

    def _log_offsets(self):
        assignments = self.consumer.assignment()
        self._log(f"*** Current Offsets: {len(assignments)}")
        for topicPartition in assignments:
            self._log(f" - {topicPartition.topic} - {topicPartition.offset}")
        self._log("")

    def _log_especial_values(self):
        self._log("*** Special Values:")
        self._log(f"- OFFSET_BEGINNING: {kafka.OFFSET_BEGINNING}")
        self._log(f"- OFFSET_END: {kafka.OFFSET_END}")
        self._log(f"- OFFSET_INVALID: {kafka.OFFSET_INVALID}")
        self._log(f"- OFFSET_STORED: {kafka.OFFSET_STORED}")
        self._log(f"- TIMESTAMP_NOT_AVAILABLE: {kafka.TIMESTAMP_NOT_AVAILABLE}")
        self._log(f"- TIMESTAMP_CREATE_TIME: {kafka.TIMESTAMP_CREATE_TIME}")
        self._log(f"- TIMESTAMP_LOG_APPEND_TIME: {kafka.TIMESTAMP_LOG_APPEND_TIME}")
        self._log("")

    def _show_startup_banner(self):
        """Mostra banner de inicialização estilo Spring Boot."""
        import platform
        import sys

        # Ler banner do arquivo
        try:
            banner_path = os.path.join(os.path.dirname(__file__), 'banner.txt')
            with open(banner_path, 'r') as f:
                banner = f.read()
        except FileNotFoundError:
            # Fallback caso o arquivo não exista
            banner = "Kafka Data Indexer"

        # Informações da aplicação
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"

        self.logger.info(f"\n{banner}")
        self.logger.info("Kafka Data Indexer")
        self.logger.info("")
        self.logger.info(f"Python Version    : {python_version}")
        self.logger.info(f"Platform          : {platform.platform()}")
        self.logger.info(f"Kafka Servers     : {self.config.get('bootstrap.servers', 'N/A')}")
        self.logger.info(f"Consumer Group    : {self.config.get('group.id', 'N/A')}")
        self.logger.info(f"Poll Timeout      : {self.poll_timeout}s")
        self.logger.info(f"Batch Max Size    : {self.batch_max_size}")
        self.logger.info(f"Batch Max Seconds : {self.batch_max_seconds}s")
        self.logger.info(f"Registered Processors: {len(self.registeredProcessors)}")
        self.logger.info("")

    def _log(self, msg):
        """Log a message using loguru."""
        self.logger.info(msg)
