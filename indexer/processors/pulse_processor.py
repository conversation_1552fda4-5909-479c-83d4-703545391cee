import os
import typing
from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class PulseProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-analytics-pulses-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'

    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.pulse'

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            'properties': {
                'id': {'type': 'keyword'},
                'name': {
                    'type': 'text',
                    'copy_to': 'search_terms',
                    'fields': {
                        "keyword": {'type': 'keyword'},
                        "sortable": {
                            'type': 'keyword',
                            'normalizer': 'case_insensitive'
                        },
                    }
                },
                'description': {
                    'type': 'text',
                    'copy_to': 'search_terms'
                },
                'holder_image': {'type': 'keyword'},
                'status': { 'type': 'keyword' },
                'points': { 'type': 'integer' },
                'duration_time': { 'type': 'integer' },
                'language': {'type': 'keyword'},
                'learn_content_url': {'type': 'keyword'},

                'workspace_id': {'type': 'keyword'},
                'created_date': { 'type': 'date' },
                'updated_date': { 'type': 'date' },
                'is_active': {'type': 'boolean'},

                'user_creator': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'avatar': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'},
                                "sortable": {
                                    'type': 'keyword',
                                    'normalizer': 'case_insensitive'
                                },
                            }
                        },
                    }
                },

                'pulse_type': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'}
                            }
                        },
                    }
                },

                'channels': {
                    'type': 'nested',
                    'properties': {
                        'relation_id': {'type': 'keyword'},
                        'id': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'}
                            }
                        },
                        'is_active': {'type': 'boolean'},
                        'description': { 'type': 'text' },
                        'category_id': {'type': 'keyword'},
                        'category_name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'},
                            }
                        },
                        'type_id': {'type': 'keyword'},
                        'type_name': {'type': 'keyword'},
                        'workspace_id': {'type': 'keyword'},
                    }
                },

                "search_terms": {
                    "type": "search_as_you_type",
                    "analyzer": "folding",
                },
            }
        }

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        with self.db.konquest_engine.connect() as konquest_conn:

            docs = []

            in_ids = ','.join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, 'konquest/pulses_chunk.sql', pulses_ids=in_ids)

            for i, row in enumerate(rs):
                pulse_id = self.str_id(row['id'])
                self.log(f'[{pulse_id}] -- Processing {i+1} of {len(updated_ids)}')

                # Many/One to One
                _type = {
                    'id': self.str_id(row['pulse_type_id']),
                    'name': row['pulse_type_name']
                }

                _user_creator = {
                    'id': self.str_id(row['user_creator_id']),
                    'name': row['user_creator_name'],
                    'avatar': row['user_creator_avatar'],
                }

                # One to Many
                _channels = self._query_pulse_channels(konquest_conn, pulse_id)

                # One to One
                _learn_content_url = self._query_learn_content_url(row['learn_content_id'])

                pulse = {
                    '_id': pulse_id,  # elastic index id
                    'id': pulse_id,
                    'name': row['name'],
                    'description': row['description'],
                    'holder_image': row['holder_image'],
                    'learn_content_url': _learn_content_url,

                    'status': row['status'],
                    'points': row['points'],
                    'duration_time': row['duration_time'],
                    'language': row['language'],

                    'workspace_id': self.str_id(row['workspace_id']),
                    'created_date': self.str_date(row['created_date']),
                    'updated_date': self.str_date(row['updated_date']),
                    'is_active': row['is_active'],

                    'user_creator': _user_creator,
                    'pulse_type': _type,
                    'channels': _channels,
                }

                docs.append(pulse)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)

        return True

    def _query_pulse_channels(self, konquest_conn, pulse_id):
        channels = []

        rs = self.db.run_sql(konquest_conn, 'konquest/pulse_channels.sql', pulse_id=pulse_id)
        for row in rs:
            channel = {
                'relation_id': self.str_id(row['id']),
                'id': self.str_id(row['channel_id']),
                'is_active': row['channel_is_active'],
                'name': row['channel_name'],
                'description': row['channel_description'],
                'category_id': self.str_id(row['channel_category_id']),
                'category_name': row['channel_category_name'],
                'type_id': self.str_id(row['channel_type_id']),
                'type_name': row['channel_type_name'],
                'workspace_id': self.str_id(row['workspace_id']),
            }
            channels.append(channel)
        return channels

    def _query_learn_content_url(self, pulse_id) -> typing.Dict[str, str]:
        with self.db.kontent_engine.connect() as kontent_conn:
            rs = self.db.run_sql(kontent_conn, 'kontent/content_url_for_contents.sql', pulse_id=pulse_id)
            row = next(rs, None)
        
        return row['learn_content_url'] if row else None
