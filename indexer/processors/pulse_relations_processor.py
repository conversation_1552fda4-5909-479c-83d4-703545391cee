import os
import typing
from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class PulseRelationsProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-analytics-pulses-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'
    USER_TOPIC = f'pg_myaccount_{ENV_SUFFIX}.public.user'

    def get_kafka_main_topic(self) -> str:
        return None

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        tables = [
            'pulse_type',
            'pulse_channel',
            'channel',
            'channel_category',
            'channel_type',
        ]
        topics = {f'{self.TOPIC_PREFIX}.{table}': None for table in tables}
        topics[self.USER_TOPIC] = None
        return topics

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    # Skip pre-processing for relations
    def pre_process_batch(self, batch: typing.List[KeepsMessage]):
        return self.do_process_batch(batch, None)

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        # ignore deleted and first time read related messages
        updated_msgs = [msg for msg in batch if msg.op not in ['d', 'r']]

        users = [user for user in updated_msgs if user.topic == self.USER_TOPIC]

        pulse_types = [pulse_type for pulse_type in updated_msgs if pulse_type.topic == self.TOPIC_PREFIX + '.pulse_type']
        channels = [channel for channel in updated_msgs if channel.topic == self.TOPIC_PREFIX + '.channel']
        channel_categories = [channel_category for channel_category in updated_msgs if channel_category.topic == self.TOPIC_PREFIX + '.channel_category']
        channel_types = [channel_type for channel_type in updated_msgs if channel_type.topic == self.TOPIC_PREFIX + '.channel_type']

        # include deleted for many to many relations
        updated_or_deleted_msgs = [msg for msg in batch if msg.op not in ['r']]

        pulse_channel = [pair for pair in updated_or_deleted_msgs if pair.topic == self.TOPIC_PREFIX + '.pulse_channel']

        self.log(
            f'-- {len(batch)} messages, {len(users)} users, {len(pulse_types)} pulse types, '
            f'-- {len(channels)} channels, {len(channel_categories)} channel categories, {len(channel_types)} channel types'
        )

        for user in users:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'term': { 'user_creator.id': user.id}
                },
                'script': {
                    'lang': 'painless',
                    'source': 'ctx._source.user_creator.name = params["name"]',
                    'params': {
                        'name': user.entity("name")
                    }
                },
            })

        for pulse_type in pulse_types:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'term': { 'pulse_type.id': pulse_type.id}
                },
                'script': {
                    'lang': 'painless',
                    'source': 'ctx._source.pulse_type.name = params["name"]',
                    'params': {
                        'name': pulse_type.entity("name")
                    }
                },
            })

        for channel in channels:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'channels',
                        'query': {
                            'bool': {
                                'filter': [
                                    { 'term': { 'channels.id': channel.id } }
                                ]
                            }
                        }
                    },
                },
                'script': {
                    'lang': 'painless',
                    'source': '''
                        def targets = ctx._source.channels.findAll(ch -> ch.id == params['id']);
                        for(ch in targets) {
                            ch.name = params['name']
                        }
                        '''.replace('\n', ''),
                    'params': {
                        'id': channel.id,
                        'name': channel.entity("name")
                    }
                }
            })

        for channel_category in channel_categories:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'channels',
                        'query': {
                            'bool': {
                                'filter': [
                                    { 'term': { 'channels.category_id': channel_category.id } }
                                ]
                            }
                        }
                    },
                },
                'script': {
                    'lang': 'painless',
                    'source': '''
                        def targets = ctx._source.channels.findAll(ch -> ch.category_id == params['category_id']);
                        for(ch in targets) {
                            ch.name = params['category_name']
                        }
                        '''.replace('\n', ''),
                    'params': {
                        'category_id': channel_category.id,
                        'category_name': channel_category.entity("name")
                    }
                }
            })

        for channel_type in channel_types:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'channels',
                        'query': {
                            'bool': {
                                'filter': [
                                    { 'term': { 'channels.type_id': channel_type.id } }
                                ]
                            }
                        }
                    },
                },
                'script': {
                    'lang': 'painless',
                    'source': '''
                        def targets = ctx._source.channels.findAll(ch -> ch.type_id == params['type_id']);
                        for(ch in targets) {
                            ch.name = params['type_name']
                        }
                        '''.replace('\n', ''),
                    'params': {
                        'type_id': channel_type.id,
                        'type_name': channel_type.entity("name")
                    }
                }
            })

        # Pulse Channels (maybe is unnecessary)

        pulses_ids_with_added_channel = set([pair.entity('pulse_id') for pair in pulse_channel if pair.op in ['c', 'u']])
        with self.db.konquest_engine.connect() as konquest_conn:
            for pulse_id in pulses_ids_with_added_channel:
                channels = self._query_pulse_channels(konquest_conn, pulse_id)
                self.log(f'Updating pulses for pulse "{pulse_id}" with {len(channels)} channels')

                self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                    'query': {
                        'term': { 'id': pulse_id }
                    },
                    'script': {
                        'lang': 'painless',
                        'source': 'ctx._source.channels = params["channels"]',
                        'params': {
                            'channels': channels
                        }
                    },
                })

        deleted_pulse_channels_ids = [pair.id for pair in pulse_channel if pair.op in ['d']]
        for deleted_pulse_channel_id in deleted_pulse_channels_ids:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'channels',
                        'query': {
                            'bool': {
                                'filter': [
                                    { 'term': { 'channels.relation_id': deleted_pulse_channel_id } }
                                ]
                            }
                        }
                    },
                },
                "script": {
                    "source": "ctx._source.channels.removeIf(channel -> channel.relation_id == params.relationId)",
                    "lang": "painless",
                    "params": {
                        "relationId": deleted_pulse_channel_id
                    }
                }
            })

        return True

    def _query_pulse_channels(self, konquest_conn, pulse_id):
        channels = []

        rs = self.db.run_sql(konquest_conn, 'konquest/pulse_channels.sql', pulse_id=pulse_id)
        for row in rs:
            channel = {
                'relation_id': self.str_id(row['id']),
                'id': self.str_id(row['channel_id']),
                'name': row['channel_name'],
                'is_active': row['channel_is_active'],
                'description': row['channel_description'],
                'category_id': self.str_id(row['channel_category_id']),
                'category_name': row['channel_category_name'],
                'type_id': self.str_id(row['channel_type_id']),
                'type_name': row['channel_type_name'],
                'workspace_id': self.str_id(row['workspace_id']),
            }
            channels.append(channel)
        return channels
