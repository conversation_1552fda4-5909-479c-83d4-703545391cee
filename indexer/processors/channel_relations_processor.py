import os
import typing
from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class ChannelRelationsProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-analytics-channels-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'
    USER_TOPIC = f'pg_myaccount_{ENV_SUFFIX}.public.user'

    def get_kafka_main_topic(self) -> str:
        None

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        tables = [
            'channel_category',
            'channel_type',
            'channel_contributor',
            'pulse',
            'pulse_channel',
        ]
        topics = {f'{self.TOPIC_PREFIX}.{table}': None for table in tables}
        topics[self.USER_TOPIC] = None
        return topics

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    # Skip pre-processing for relations
    def pre_process_batch(self, batch: typing.List[KeepsMessage]):
        return self.do_process_batch(batch, None)

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        # ignore deleted and first time read many to one relations
        updated_msgs = [msg for msg in batch if msg.op not in ['d', 'r']]

        users = [user for user in updated_msgs if user.topic == self.USER_TOPIC]

        categories = [category for category in updated_msgs if category.topic == self.TOPIC_PREFIX + '.channel_category']
        types = [type for type in updated_msgs if type.topic == self.TOPIC_PREFIX + '.channel_type']
        pulses = [pulse for pulse in updated_msgs if pulse.topic == self.TOPIC_PREFIX + '.pulse']

        # include deleted for many to many relations
        updated_or_deleted_msgs = [msg for msg in batch if msg.op not in ['r']]

        channel_contributors = [contributor for contributor in updated_or_deleted_msgs if contributor.topic == self.TOPIC_PREFIX + '.channel_contributor']
        pulse_channel = [pair for pair in updated_or_deleted_msgs if pair.topic == self.TOPIC_PREFIX + '.pulse_channel']

        self.log(f'-- {len(batch)} messages, {len(users)} users, {len(categories)} categories, {len(types)} types, {len(channel_contributors)} contributors, {len(types)} pulses') # noqa

        for user in users:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'term': { 'user_creator.id': user.id}
                },
                'script': {
                    'lang': 'painless',
                    'source': 'ctx._source.user_creator.name = params["name"]',
                    'params': {
                        'name': user.entity("name")
                    }
                },
            })

        for category in categories:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'term': { 'channel_category_id': category.id}
                },
                'script': {
                    'lang': 'painless',
                    'source': 'ctx._source.channel_category_name = params["name"]',
                    'params': {
                        'name': category.entity("name")
                    }
                },
            })

        for type in types:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'term': { 'channel_type_id': type.id}
                },
                'script': {
                    'lang': 'painless',
                    'source': 'ctx._source.channel_type_name = params["name"]',
                    'params': {
                        'name': type.entity("name")
                    }
                },
            })

        for pulse in pulses:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'pulses',
                        'query': {
                            'bool': {
                                'filter': [
                                    { 'term': { 'pulses.id': pulse.id } }
                                ]
                            }
                        }
                    },
                },
                'script': {
                    'lang': 'painless',
                    'source': '''
                        def targets = ctx._source.pulses.findAll(pulse -> pulse.id == params['id']);
                        for(pulse in targets) {
                            pulse.name = params['name']
                            pulse.description = params['description']
                            pulse.duration_time = params['duration_time']
                        }
                        '''.replace('\n', ''),
                    'params': {
                        'id': pulse.id,
                        'name': pulse.entity("name"),
                        'description': pulse.entity("description"),
                        'duration_time': pulse.entity("duration_time"),
                    }
                }
            })

        # Contributors
        channels_ids_with_updated_contributors = set([contributor.entity('channel_id') for contributor in channel_contributors if contributor.op in ['c', 'u']])
        with self.db.konquest_engine.connect() as konquest_conn:
            for channel_id in channels_ids_with_updated_contributors:
                contributors = self._query_channel_contributors(konquest_conn, channel_id)
                self.log(f'Updating contributors for channel "{channel_id}" with {len(contributors)} contributors')

                self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                    'query': {
                        'term': { 'id': channel_id }
                    },
                    'script': {
                        'lang': 'painless',
                        'source': 'ctx._source.contributors = params["contributors"]',
                        'params': {
                            'contributors': contributors
                        }
                    },
                })

        deleted_channel_contributors_ids = [contributor.id for contributor in channel_contributors if contributor.op in ['d']]
        for deleted_channel_contributor_id in deleted_channel_contributors_ids:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'contributors',
                        'query': {
                            'bool': {
                                'filter': [
                                    { 'term': { 'contributors.relation_id': deleted_channel_contributor_id } }
                                ]
                            }
                        }
                    },
                },
                "script": {
                    "source": "ctx._source.contributors.removeIf(contributor -> contributor.relation_id == params.relationId)",
                    "lang": "painless",
                    "params": {
                        "relationId": deleted_channel_contributor_id
                    }
                }
            })

        # Channel Pulses

        channels_ids_with_updated_pulses = set([pair.entity('channel_id') for pair in pulse_channel if pair.op in ['c', 'u']])
        with self.db.konquest_engine.connect() as konquest_conn:
            for channel_id in channels_ids_with_updated_pulses:
                pulses = self._query_channel_pulses(konquest_conn, channel_id)
                self.log(f'Updating pulses for channel "{channel_id}" with {len(pulses)} pulses')

                self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                    'query': {
                        'term': { 'id': channel_id }
                    },
                    'script': {
                        'lang': 'painless',
                        'source': 'ctx._source.pulses = params["pulses"]',
                        'params': {
                            'pulses': pulses
                        }
                    },
                })

        deleted_pulse_channels_ids = [pair.id for pair in pulse_channel if pair.op in ['d']]
        for deleted_channel_pulse_id in deleted_pulse_channels_ids:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'pulses',
                        'query': {
                            'bool': {
                                'filter': [
                                    { 'term': { 'pulses.relation_id': deleted_channel_pulse_id } }
                                ]
                            }
                        }
                    },
                },
                "script": {
                    "source": "ctx._source.pulses.removeIf(pulse -> pulse.relation_id == params.relationId)",
                    "lang": "painless",
                    "params": {
                        "relationId": deleted_channel_pulse_id
                    }
                }
            })

        return True

    def _query_channel_contributors(self, konquest_conn, channel_id):
        contributors = []

        rs = self.db.run_sql(konquest_conn, 'konquest/channel_contributors.sql', channel_id=channel_id)
        for row in rs:
            contributors.append({
                'relation_id': self.str_id(row['id']),
                'user_id': self.str_id(row['user_id']),
                'created_date': self.str_date(row['created_date']),
                'updated_date': self.str_date(row['updated_date']),
            })
        return contributors

    def _query_channel_pulses(self, konquest_conn, channel_id):
        pulses = []

        rs = self.db.run_sql(konquest_conn, 'konquest/channel_pulses.sql', channel_id=channel_id)
        for row in rs:
            channel = {
                'relation_id': self.str_id(row['id']),
                'id': self.str_id(row['channel_id']),
                'name': row['pulse_name'],
                'description': row['pulse_description'],
                'duration_time': row['duration_time'],
            }
            pulses.append(channel)
        return pulses
