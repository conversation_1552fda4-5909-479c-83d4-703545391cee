import os
import typing

from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')

QUIZ_CONTENT_TYPE_ID = '7a41a8e0-ee37-4d0b-ad4f-35bada67134d'
QUIZ_CONTENT_TYPE_NAME = 'Question'


class CourseProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-analytics-courses-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'

    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.mission'

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        return {
            f'{self.TOPIC_PREFIX}.mission_stage': lambda msg: msg.entity('mission_id'),
            f'{self.TOPIC_PREFIX}.mission_stage_content': lambda msg: None,  # group and query
        }

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            'properties': {
                'id': { 'type': 'keyword' },
                'name': {
                    'type': 'text',
                    'copy_to': 'search_terms',
                    'fields': {
                        "keyword": {'type': 'keyword'},
                        "sortable": {
                            'type': 'keyword',
                            'normalizer': 'case_insensitive'
                        },
                    }
                },
                'description': {
                    'type': 'text',
                    'copy_to': 'search_terms'
                },
                'duration_time': { 'type': 'integer' },
                'points': { 'type': 'integer' },
                'development_status': { 'type': 'keyword' },
                'is_active': { 'type': 'boolean' },

                'thumb_image': { 'type': 'keyword' },
                'holder_image': { 'type': 'keyword' },
                'vertical_holder_image': { 'type': 'keyword' },
                'summary': { 'type': 'text' },
                'language': { 'type': 'keyword' },
                'course_model': { 'type': 'keyword' },
                'assessment_type': { 'type': 'keyword' },
                'required_evaluation': { 'type': 'boolean' },
                'allow_self_enrollment_renewal': { 'type': 'boolean' },
                'allow_self_reproved_enrollment_renewal': { 'type': 'boolean' },
                'minimum_performance': {
                    'type': 'scaled_float',
                    'scaling_factor': 1000,
                },

                'created_date': { 'type': 'date' },
                'updated_date': { 'type': 'date' },
                'expiration_date': { 'type': 'date' },

                "search_terms": {
                    "type": "search_as_you_type",
                    "analyzer": "folding",
                },

                'workspaces': {
                    'type': 'nested',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'created_date': {'type': 'date'},
                        'updated_date': {'type': 'date'},
                        'workspace_id': {'type': 'keyword'},
                        'relationship_type': {'type': 'keyword'},
                        'minimum_performance': {
                            'type': 'scaled_float',
                            'scaling_factor': 1000,
                        },
                    }
                },

                'user_creator': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'copy_to': 'user_creator.search_terms',
                            'fields': {
                                "keyword": {'type': 'keyword'},
                                "sortable": {
                                    'type': 'keyword',
                                    'normalizer': 'case_insensitive'
                                },
                            }
                        },
                        'status': {'type': 'keyword'},
                        'email': {'type': 'keyword'},
                        'avatar': {'type': 'keyword'},

                        "search_terms": {
                            "type": "search_as_you_type",
                            "analyzer": "folding",
                        },
                    }
                },

                'course_type': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'}
                            }
                        },
                    }
                },

                'course_category': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'},
                                "sortable": {
                                    'type': 'keyword',
                                    'normalizer': 'case_insensitive'
                                },
                            }
                        },
                    }
                },

                'course_contents': {
                    'type': 'nested',
                    'properties': {
                        'stage_id': {'type': 'keyword'},
                        'stage_order': {'type': 'integer'},
                        'stage_name': {'type': 'keyword'},

                        'content_id': {'type': 'keyword'},
                        'content_order': {'type': 'integer'},
                        'content_name': {
                            'type': 'text',
                            'copy_to': 'course_contents.search_terms',
                            'fields': {
                                "keyword": {'type': 'keyword'},
                                "sortable": {
                                    'type': 'keyword',
                                    'normalizer': 'case_insensitive'
                                },
                            }
                        },

                        'stage_content_type': {'type': 'keyword'},

                        'kontent_id': {'type': 'keyword'},
                        'content_type_id': {'type': 'keyword'},
                        'content_type_name': {'type': 'keyword'},

                        "search_terms": {
                            "type": "search_as_you_type",
                            "analyzer": "folding",
                        },
                    }
                },

                'external_course': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'course_type': {'type': 'keyword'},
                        'course_url': {'type': 'keyword'},
                        'provider_id': {'type': 'keyword'},
                        'provider_name': {'type': 'keyword'},
                        'provider_description': {'type': 'keyword'},
                        'provider_icon': {'type': 'keyword'},
                    }
                },

                'presential_course': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'address': {'type': 'keyword'},
                        'seats': {'type': 'integer'},
                        'remaining_seats': {'type': 'integer'},
                        'next_date': {'type': 'date'},
                        'notify_users_enrolled': {'type': 'boolean'},
                        'allow_any_enrollment': {'type': 'boolean'},
                    }
                },

                'live_course': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'url': {'type': 'keyword'},
                        'seats': {'type': 'integer'},
                        'remaining_seats': {'type': 'integer'},
                        'next_date': {'type': 'date'},
                        'notify_users_enrolled': {'type': 'boolean'},
                        'allow_any_enrollment': {'type': 'boolean'},
                    }
                },

                'contributors': {
                    'type': 'nested',
                    'properties': {
                        'relation_id': {'type': 'keyword'},
                        'user_id': {'type': 'keyword'},
                        'created_date': {'type': 'date'},
                        'updated_date': {'type': 'date'},
                    }
                },

                'instructors': {
                    'type': 'nested',
                    'properties': {
                        'relation_id': {'type': 'keyword'},
                        'user_id': {'type': 'keyword'},
                    }
                },

                'tags': {
                    'type': 'nested',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'name': {'type': 'keyword'},
                        'relevance': {
                            'type': 'scaled_float',
                            'scaling_factor': 1000000,
                        },
                    }
                },

            }
        }

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        with self.db.konquest_engine.connect() as konquest_conn,\
             self.db.kontent_engine.connect() as kontent_conn:

            updated_ids.update(self._query_mission_ids_from_content(konquest_conn, batch))
            updated_ids.difference_update(self.get_deleted_ids())

            self.log(f'-- {len(batch)} messages, {len(updated_ids)} updated including content changes.')

            docs = []

            in_ids = ','.join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, 'konquest/missions_chunk.sql', missions_ids=in_ids)

            for i, row in enumerate(rs):
                mission_id = self.str_id(row['mission_id'])
                self.log(f'[{mission_id}] -- Processing {i+1} of {len(updated_ids)}')

                # Many/One to One
                _category = {
                    'id': self.str_id(row['mission_category_id']),
                    'name': row['mission_category_name']
                }

                _type = {
                    'id': self.str_id(row['mission_type_id']),
                    'name': row['mission_type_name']
                }

                _user_creator = {
                    'id': self.str_id(row['user_creator_id']),
                    'name': row['user_creator_name'],
                    'status': row['user_creator_status'],
                    'email': row['user_creator_email'],
                    'avatar': row['user_creator_avatar'],
                }

                _course_external = {
                    'id': self.str_id(row['mission_external_id']),
                    'course_type': row['mission_external_course_type'],
                    'course_url': row['mission_external_course_url'],
                    'provider_id': row['mission_external_provider_id'],
                    'provider_name': row['mission_external_provider_name'],
                    'provider_description': row['mission_external_provider_description'],
                    'provider_icon': row['mission_external_provider_icon'],
                }
                if not _course_external['id']:
                    _course_external = None

                _course_presential = {
                    'id': self.str_id(row['mission_presential_id']),
                    'address': row['mission_presential_address'],
                    'seats': row['mission_presential_seats'],
                    'remaining_seats': row['mission_presential_remaining_seats'],
                    'next_date': self.str_date(row['mission_presential_next_date']),
                    'notify_users_enrolled': row['mission_presential_notify_users_enrolled'],
                    'allow_any_enrollment': row['mission_presential_allow_any_enrollment'],
                }

                if not _course_presential['id']:
                    _course_presential = None

                _course_live = {
                    'id': self.str_id(row['mission_live_id']),
                    'url': row['mission_live_url'],
                    'seats': row['mission_live_seats'],
                    'remaining_seats': row['mission_live_remaining_seats'],
                    'next_date': self.str_date(row['mission_live_next_date']),
                    'notify_users_enrolled': row['mission_live_notify_users_enrolled'],
                    'allow_any_enrollment': row['mission_live_allow_any_enrollment'],
                }
                if not _course_live['id']:
                    _course_live = None

                # One to Many
                workspaces = self._query_course_workspaces(konquest_conn, mission_id)

                _contents = self._query_mission_contents(konquest_conn, mission_id)
                self._populate_content_types(kontent_conn, _contents)

                _contributors = self._query_mission_contributors(konquest_conn, mission_id)

                _instructors = None

                if _course_live is not None and _course_live['id']:
                    _instructors = self._query_live_instructors(konquest_conn, mission_id)

                if _course_presential is not None and _course_presential['id']:
                    _instructors = self._query_presential_instructors(konquest_conn, mission_id)

                _tags = self._query_mission_tags(konquest_conn, mission_id)

                # The doc
                course = {
                    '_id': mission_id,  # elastic index id
                    'id': mission_id,
                    'name': row['name'],
                    'description': row['description'],
                    'duration_time': row['duration_time'],
                    'points': row['points'],
                    'is_active': row['is_active'],
                    'development_status': row['development_status'],

                    'thumb_image': row['thumb_image'],
                    'holder_image': row['holder_image'],
                    'vertical_holder_image': row['vertical_holder_image'],
                    'summary': row['summary'],
                    'language': row['language'],
                    'course_model': row['mission_model'],
                    'assessment_type': row['assessment_type'],
                    'required_evaluation': row['required_evaluation'],
                    'allow_self_enrollment_renewal': row['allow_self_enrollment_renewal'],
                    'allow_self_reproved_enrollment_renewal': row['allow_self_reproved_enrollment_renewal'],
                    'minimum_performance': row['minimum_performance'],

                    'created_date': self.str_date(row['created_date']),
                    'updated_date': self.str_date(row['updated_date']),
                    'expiration_date': self.str_date(row['expiration_date']),

                    'workspaces': workspaces,
                    'user_creator': _user_creator,
                    'course_type': _type,
                    'course_category': _category,
                    'course_contents': _contents,
                    'contributors': _contributors,
                    'instructors': _instructors,
                    'tags': _tags,

                    'external_course': _course_external,
                    'presential_course': _course_presential,
                    'live_course': _course_live,
                }
                docs.append(course)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)          

        return True

    def _query_mission_ids_from_content(self, konquest_conn, batch: typing.List[KeepsMessage]):
        missions_ids = []

        contents_ids = [content.id for content in batch if content.topic == self.TOPIC_PREFIX + '.mission_stage_content']
        if contents_ids:
            in_ids = ','.join([f"'{id}'" for id in contents_ids])
            rs = self.db.run_sql(konquest_conn, 'konquest/missions_from_contents.sql', contents_ids=in_ids)
            missions_ids = [row['mission_id'] for row in rs]

        return missions_ids

    def _query_course_workspaces(self, konquest_conn, mission_id):
        workspaces = []

        rs = self.db.run_sql(konquest_conn, 'konquest/mission_workspaces.sql', mission_id=mission_id)
        for row in rs:
            workspace = {
                'id': self.str_id(row['id']),
                'created_date': self.str_date(row['created_date']),
                'updated_date': self.str_date(row['updated_date']),
                'workspace_id': self.str_id(row['workspace_id']),
                'relationship_type': row['relationship_type'],
                'minimum_performance': row['min_performance_certificate']
            }
            workspaces.append(workspace)
        return workspaces

    def _query_mission_contents(self, konquest_conn, mission_id):
        contents = []

        rs = self.db.run_sql(konquest_conn, 'konquest/mission_contents.sql', mission_id=mission_id)
        for row in rs:
            contents.append({
                'stage_id': self.str_id(row['stage_id']),
                'stage_name': row['stage_name'],
                'stage_order': row['stage_order'],
                'content_id': self.str_id(row['content_id']),
                'content_name': row['content_name'],
                'content_order': row['content_order'],
                'stage_content_type': row['stage_content_type'],
                'kontent_id': row['kontent_content_id'],
            })
        return contents

    def _populate_content_types(self, kontent_conn, contents):
        kontent_ids = list()
        content_map = dict()

        for content in contents:

            # Exam has special treatment
            if content['stage_content_type'] == 'EXAM':
                content.update({
                    'content_type_id': QUIZ_CONTENT_TYPE_ID,
                    'content_type_name': QUIZ_CONTENT_TYPE_NAME,
                })
                continue

            if not content['kontent_id']:
                continue

            # Otherwise get types from Kontent DB
            kontent_id = self.str_id(content['kontent_id'])
            kontent_ids.append(kontent_id)
            content_map[kontent_id] = content

        if not kontent_ids:
            return

        in_ids = ','.join([f"'{id}'" for id in kontent_ids])
        rs = self.db.run_sql(kontent_conn, 'kontent/content_types_for_contents.sql', kontent_ids=in_ids)

        for row in rs:
            kontent_id = self.str_id(row['learn_content_id'])
            content = content_map[kontent_id]
            content.update({
                'content_type_id': self.str_id(row['content_type_id']),
                'content_type_name': row['content_type_name']
            })

        return contents

    def _query_mission_contributors(self, konquest_conn, mission_id):
        contributors = []

        rs = self.db.run_sql(konquest_conn, 'konquest/mission_contributors.sql', mission_id=mission_id)
        for row in rs:
            contributors.append({
                'relation_id': self.str_id(row['id']),
                'user_id': self.str_id(row['user_id']),
                'created_date': self.str_date(row['created_date']),
                'updated_date': self.str_date(row['updated_date']),
            })
        return contributors


    def _query_live_instructors(self, konquest_conn, mission_id):
        instructors = []
        rs = self.db.run_sql(konquest_conn, 'konquest/live_mission_instructors.sql', mission_id=mission_id)
        for row in rs:
            instructors.append({
                'relation_id': self.str_id(row['id']),
                'user_id': self.str_id(row['user_id']),
            })
        return instructors


    def _query_presential_instructors(self, konquest_conn, mission_id):
        instructors = []
        rs = self.db.run_sql(konquest_conn, 'konquest/presential_mission_instructors.sql', mission_id=mission_id)
        for row in rs:
            instructors.append({
                'relation_id': self.str_id(row['id']),
                'user_id': self.str_id(row['user_id']),
            })
        return instructors

    def _query_mission_tags(self, konquest_conn, mission_id):
        tags = []

        rs = self.db.run_sql(konquest_conn, 'konquest/mission_tags.sql', mission_id=mission_id)
        for row in rs:
            tags.append({
                'id': self.str_id(row['id']),
                'name': row['name'],
                'relevance': row['relevance'],
            })
        return tags
