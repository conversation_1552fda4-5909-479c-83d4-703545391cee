import os
import typing

from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class LearningTrailRelationsProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-analytics-learning-trails-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'
    USER_TOPIC = f'pg_myaccount_{ENV_SUFFIX}.public.user'

    def get_kafka_main_topic(self) -> str:
        return None

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        tables = [
            'learning_trail_type',
            'learning_trail_step',
            'mission',
            'pulse',
            'channel',
            'channel_category',
        ]
        topics = {f'{self.TOPIC_PREFIX}.{table}': None for table in tables}
        topics[self.USER_TOPIC] = None
        return topics

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    # Skip pre-processing for relations
    def pre_process_batch(self, batch: typing.List[KeepsMessage]):
        return self.do_process_batch(batch, None)

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        # ignore deleted and first time read related messages
        updated_msgs = [msg for msg in batch if msg.op not in ['d', 'r']]

        users = [user for user in updated_msgs if user.topic == self.USER_TOPIC]

        learning_trail_types = [trail_type for trail_type in updated_msgs if trail_type.topic == self.TOPIC_PREFIX + '.learning_trail_type']
        learning_trail_steps = [trail_step for trail_step in updated_msgs if trail_step.topic == self.TOPIC_PREFIX + '.learning_trail_step']
        missions = [mission for mission in updated_msgs if mission.topic == self.TOPIC_PREFIX + '.mission']
        pulses = [pulse for pulse in updated_msgs if pulse.topic == self.TOPIC_PREFIX + '.pulse']
        channels = [channel for channel in updated_msgs if channel.topic == self.TOPIC_PREFIX + '.channel']
        channel_categories = [channel_category for channel_category in updated_msgs if channel_category.topic == self.TOPIC_PREFIX + '.channel_category']

        self.log(
            f'-- {len(batch)} messages, {len(users)} users, {len(learning_trail_types)} learning trail types, '
            f'{len(learning_trail_steps)} learning trails steps, {len(missions)} missions, '
            f'{len(pulses)} pulses, {len(channels)} channels, {len(channel_categories)} channel categories'
        )

        for user in users:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'term': { 'user_creator.id': user.id}
                },
                'script': {
                    'lang': 'painless',
                    'source': 'ctx._source.user_creator.name = params["name"]',
                    'params': {
                        'name': user.entity("name")
                    }
                },
            })

        for trail_type in learning_trail_types:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'term': { 'trail_type.id': trail_type.id}
                },
                'script': {
                    'lang': 'painless',
                    'source': 'ctx._source.trail_type.name = params["name"]',
                    'params': {
                        'name': trail_type.entity("name")
                    }
                },
            })

        for trail_step in learning_trail_steps:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'trail_steps',
                        'query': {
                            'bool': {
                                'filter': [
                                    { 'term': { 'trail_steps.id': trail_step.id } }
                                ]
                            }
                        }
                    },
                },
                'script': {
                    'lang': 'painless',
                    'source': '''
                        def targets = ctx._source.trail_steps.findAll(step -> step.id == params['id']);
                        for(step in targets) {
                            step.step_name = params['name'];
                            step.step_order = params['order'];
                        }
                        '''.replace('\n', ''),
                    'params': {
                        'id': trail_step.id,
                        'name': trail_step.entity("name"),
                        'order': trail_step.entity("order"),
                    }
                }
            })

        for mission in missions:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'trail_steps',
                        'query': {
                            'bool': {
                                'filter': [
                                    { 'term': { 'trail_steps.mission_id': mission.id } }
                                ]
                            }
                        }
                    },
                },
                'script': {
                    'lang': 'painless',
                    'source': '''
                        def targets = ctx._source.trail_steps.findAll(step -> step.mission_id == params['id']);
                        for(step in targets) {
                            step.mission_name = params['name']
                        }
                        '''.replace('\n', ''),
                    'params': {
                        'id': mission.id,
                        'name': mission.entity("name")
                    }
                }
            })

        for pulse in pulses:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'trail_steps',
                        'query': {
                            'bool': {
                                'filter': [
                                    { 'term': { 'trail_steps.pulse_id': pulse.id } }
                                ]
                            }
                        }
                    },
                },
                'script': {
                    'lang': 'painless',
                    'source': '''
                        def targets = ctx._source.trail_steps.findAll(step -> step.pulse_id == params['id']);
                        for(step in targets) {
                            step.pulse_name = params['name']
                        }
                        '''.replace('\n', ''),
                    'params': {
                        'id': pulse.id,
                        'name': pulse.entity("name")
                    }
                }
            })

        for channel in channels:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'trail_steps',
                        'query': {
                            'nested': {
                                'path': 'trail_steps.pulse_channels',
                                'query': {
                                    'bool': {
                                        'filter': [
                                            { 'term': { 'trail_steps.pulse_channels.id': channel.id } }
                                        ]
                                    }
                                }
                            }
                        }
                    },
                },
                'script': {
                    'lang': 'painless',
                    'source': '''
                        def steps = ctx._source.trail_steps.findAll(step -> step.pulse_channels != null);
                        for(step in steps) {
                            def targets = step.pulse_channels.findAll(pc -> pc.id == params['id']);
                            for(pc in targets) {
                                pc.name = params['name']
                            }
                        }
                        '''.replace('\n', ''),
                    'params': {
                        'id': channel.id,
                        'name': channel.entity("name")
                    }
                }
            })

        for channel_category in channel_categories:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'nested': {
                        'path': 'trail_steps',
                        'query': {
                            'nested': {
                                'path': 'trail_steps.pulse_channels',
                                'query': {
                                    'bool': {
                                        'filter': [
                                            { 'term': { 'trail_steps.pulse_channels.category_id': channel_category.id } }
                                        ]
                                    }
                                }
                            }
                        }
                    },
                },
                'script': {
                    'lang': 'painless',
                    'source': '''
                        def steps = ctx._source.trail_steps.findAll(step -> step.pulse_channels != null);
                        for(step in steps) {
                            def targets = step.pulse_channels.findAll(pc -> pc.category_id == params['category_id']);
                            for(pc in targets) {
                                pc.category_name = params['category_name']
                            }
                        }
                        '''.replace('\n', ''),
                    'params': {
                        'category_id': channel_category.id,
                        'category_name': channel_category.entity("name")
                    }
                }
            })

        return True
