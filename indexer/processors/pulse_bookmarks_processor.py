import os
import typing
from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class PulseBookmarksProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-konquest-pulse-bookmarks-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'

    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.pulse_bookmark'

    def get_kafka_sub_topics(self) -> None:
        return None

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> typing.Dict[str, typing.Any]:
        return {
            'properties': {
                'id': { 'type': 'keyword' },
                'user_id': {'type': 'keyword'},
                'pulse_id': {'type': 'keyword'},
                'created_date': {'type': 'date'},
                'updated_date': {'type': 'date'},
            }
        }

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        with self.db.konquest_engine.connect() as konquest_conn:

            docs = []

            in_ids = ','.join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, 'konquest/pulse_bookmarks_chunk.sql', bookmarks_ids=in_ids)

            for i, row in enumerate(rs):
                bookmark_id = self.str_id(row['id'])
                self.log(f'[{bookmark_id}] -- Processing {i+1} of {len(updated_ids)}')

                bookmarks = {
                    '_id': bookmark_id,  # elastic index id
                    'id': bookmark_id,
                    'user_id': self.str_id(row['user_id']),
                    'pulse_id': self.str_id(row['pulse_id']),
                    'created_date': self.str_date(row['created_date']),
                    'updated_date': self.str_date(row['updated_date'])
                }

                docs.append(bookmarks)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)

        return True
