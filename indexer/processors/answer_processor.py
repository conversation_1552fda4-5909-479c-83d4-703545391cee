import os
import typing
from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class AnswerProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-analytics-answers-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'

    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.answer'

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            'properties': {
                'id': {'type': 'keyword'},
                'is_ok': {'type': 'boolean'},
                'user_id': {'type': 'keyword'},
                'question_id': {'type': 'keyword'},
                'question_points': {'type': 'integer'},
                'question_name': {
                    'type': 'text',
                    'copy_to': 'search_terms',
                    'fields': {
                        "keyword": {'type': 'keyword'},
                    }
                },
                'exam_id': {'type': 'keyword'},
                'exam_name': {
                    'type': 'text',
                    'copy_to': 'search_terms',
                    'fields': {
                        "keyword": {'type': 'keyword'},
                    }
                },
                'pulse_id': {'type': 'keyword'},
                'channel_id': {'type': 'keyword'},
                'stage_id': {'type': 'keyword'},
                'course_id': {'type': 'keyword'},
                'enrollment_id': {'type': 'keyword'},

                'created_date': { 'type': 'date' },
                'updated_date': { 'type': 'date' },

                "search_terms": {
                    "type": "search_as_you_type",
                    "analyzer": "folding",
                },
            }
        }

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        with self.db.konquest_engine.connect() as konquest_conn:

            docs = []

            in_ids = ','.join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, 'konquest/answers_chunk.sql', answers_ids=in_ids)

            for i, row in enumerate(rs):
                answer_id = self.str_id(row['id'])
                self.log(f'[{answer_id}] -- Processing {i+1} of {len(updated_ids)}')

                answers = {
                    '_id': answer_id,  # elastic index id
                    'id': answer_id,
                    'is_ok': row['is_ok'],
                    'user_id': self.str_id(row['user_id']),
                    'question_id': self.str_id(row['question_id']),
                    'question_points': row['question_points'],
                    'question_name': row['question_name'],

                    'exam_id': self.str_id(row['exam_id']),
                    'exam_name': row['exam_name'],

                    'pulse_id': self.str_id(row['pulse_id']),
                    'channel_id': self.str_id(row['channel_id']),
                    'stage_id': self.str_id(row['stage_id']),
                    'course_id': self.str_id(row['mission_id']),
                    'enrollment_id': self.str_id(row['enrollment_id']),

                    'created_date': self.str_date(row['created_date']),
                    'updated_date': self.str_date(row['updated_date'])
                }

                docs.append(answers)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)

        return True
