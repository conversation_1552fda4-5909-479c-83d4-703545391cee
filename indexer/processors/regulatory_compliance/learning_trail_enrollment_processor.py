import os
import typing

from sqlalchemy.engine import Row

from indexer.processors.regulatory_compliance.abstract_enrollment_processor import AbstractEnrollmentProcessor
from indexer.processors import KeepsMessage
from indexer.processors.regulatory_compliance.learning_trail_processor import LearningTrailProcessor

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class LearningTrailEnrollmentProcessor(AbstractEnrollmentProcessor):
    def __init__(self, learning_trail_processor: LearningTrailProcessor):
        super().__init__()
        self.learning_trail_processor = learning_trail_processor

    def _save_related_entities(self, batch: typing.List[KeepsMessage]):
        learning_trail_ids = set([message.entity('learning_trail_id') for message in batch])
        if not learning_trail_ids:
            return
        self.learning_trail_processor.do_process_batch([], learning_trail_ids)

    def get_select_sql_path(self) -> str:
        return 'konquest/all_learning_trail_enrollments_chunk.sql'

    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.learning_trail_enrollment'

    def _transform_row(self, pk: str, row: Row) -> dict:
        return {
            'id': pk,
            'status': row['status'],
            'user_id': self.str_id(row['user_id']),
            'learning_object_source_id': self.str_id(row['learning_trail_id']),
            'goal_date': str(row['goal_date']),
            'start_date': self.str_date(row['start_date']),
            'end_date': self.str_date(row['end_date']),
            'created_date': self.str_date(row['created_date']),
            'updated_date': self.str_date(row['updated_date']),
            'deleted_date': self.str_date(row['deleted_date']),
            'workspace_id': self.str_id(row['workspace_id']),
        }
