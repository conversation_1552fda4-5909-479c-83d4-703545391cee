import typing
from sqlite3 import Row
from indexer.processors.regulatory_compliance.abstract_enrollment_processor import AbstractEnrollmentProcessor
from indexer.processors import KeepsMessage
from indexer.processors.regulatory_compliance.mission_processor import MissionProcessor


class MissionEnrollmentProcessor(AbstractEnrollmentProcessor):

    def __init__(self, mission_processor: MissionProcessor):
        super().__init__()
        self._mission_processor = mission_processor

    def _save_related_entities(self, batch: typing.List[KeepsMessage]):
        affected_missions_ids = set([message.entity('mission_id') for message in batch])
        if not affected_missions_ids:
            return
        self._mission_processor.do_process_batch([], affected_missions_ids)

    def get_select_sql_path(self) -> str:
        return 'konquest/all_enrollments_chunk.sql'

    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.mission_enrollment'

    def _transform_row(self, pk: str, row: Row) -> dict:
        return {
            'id': pk,
            'status': row['status'],
            'user_id': self.str_id(row['user_id']),
            'learning_object_source_id': self.str_id(row['mission_id']),
            'goal_date': str(row['goal_date']),
            'start_date': self.str_date(row['start_date']),
            'end_date': self.str_date(row['end_date']),
            'created_date': self.str_date(row['created_date']),
            'updated_date': self.str_date(row['updated_date']),
            'deleted_date': self.str_date(row['deleted_date']),
            'workspace_id': self.str_id(row['workspace_id']),
        }
