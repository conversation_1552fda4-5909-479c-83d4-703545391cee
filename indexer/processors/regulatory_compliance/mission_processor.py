import typing
import os

from sqlalchemy.engine import Connection, ResultProxy, Row

from .abstract_learning_object_processor import AbstractLearningObjectProcessor

MISSION_LEARNING_OBJECT_TYPE_ID = os.getenv('MISSION_LEARNING_OBJECT_TYPE_ID', '798e50d7-8b97-4979-8728-4f9f1599bb05')


class MissionProcessor(AbstractLearningObjectProcessor):
    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.mission'

    def _transform_row(self, pk: str, row: Row, connection: Connection) -> typing.Dict:
        return {
            "source_id": pk,
            "learning_object_type_id": MISSION_LEARNING_OBJECT_TYPE_ID,
            "deleted_date": self.str_date(row["deleted_date"]),
            "name": row["name"],
            "development_status": row["development_status"]
        }

    def list_source_objects(self, ids: str, connection: Connection) -> ResultProxy:
        return self.db.run_sql(connection, "konquest/all_missions_chunk.sql", missions_ids=ids)
