import os
import typing
from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class ChannelProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-analytics-channels-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'

    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.channel'

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            'properties': {
                'id': {'type': 'keyword'},
                'name': {
                    'type': 'text',
                    'copy_to': 'search_terms',
                    'fields': {
                        "keyword": {'type': 'keyword'},
                        "sortable": {
                            'type': 'keyword',
                            'normalizer': 'case_insensitive'
                        },
                    }
                },
                'description': {
                    'type': 'text',
                    'copy_to': 'search_terms',
                },
                'language': {'type': 'keyword'},
                'is_active': {'type': 'boolean'},
                'holder_image': {'type': 'keyword'},

                'created_date': { 'type': 'date' },
                'updated_date': { 'type': 'date' },

                'workspace_id': {'type': 'keyword'},

                'user_creator': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'avatar': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'},
                            }
                        },
                    }
                },

                'channel_type': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'}
                            }
                        },
                    }
                },

                'channel_category': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'},
                            }
                        },
                    }
                },

                'contributors': {
                    'type': 'nested',
                    'properties': {
                        'relation_id': {'type': 'keyword'},
                        'user_id': {'type': 'keyword'},
                        'created_date': {'type': 'date'},
                        'updated_date': {'type': 'date'},
                    }
                },

                'pulses': {
                    'type': 'nested',
                    'properties': {
                        'relation_id': {'type': 'keyword'},
                        'id': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'}
                            }
                        },
                        'description': { 'type': 'text' },
                        'duration_time': { 'type': 'integer' },
                    }
                },

                "search_terms": {
                    "type": "search_as_you_type",
                    "analyzer": "folding",
                },
            }
        }

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        with self.db.konquest_engine.connect() as konquest_conn:

            docs = []

            in_ids = ','.join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, 'konquest/channels_chunk.sql', channels_ids=in_ids)

            for i, row in enumerate(rs):
                channel_id = self.str_id(row['id'])
                self.log(f'[{channel_id}] -- Processing {i+1} of {len(updated_ids)}')

                _user_creator = {
                    'id': self.str_id(row['user_creator_id']),
                    'name': row['user_creator_name'],
                    'avatar': row['user_creator_avatar'],
                }

                _channel_type = {
                    'id': self.str_id(row['channel_type_id']),
                    'name': row['channel_type_name'],
                }

                _channel_category = {
                    'id': self.str_id(row['channel_category_id']),
                    'name': row['channel_category_name'],
                }

                # One to Many

                _contributors = self._query_channel_contributors(konquest_conn, channel_id)

                _pulses = self._query_channel_pulses(konquest_conn, channel_id)

                channel = {
                    '_id': channel_id,  # elastic index id
                    'id': channel_id,
                    'name': row['name'],
                    'description': row['description'],
                    'language': row['language'],
                    'is_active': row['is_active'],
                    'holder_image': row['holder_image'],

                    'created_date': self.str_date(row['created_date']),
                    'updated_date': self.str_date(row['updated_date']),

                    'workspace_id': self.str_id(row['workspace_id']),
                    'user_creator_id': self.str_id(row['user_creator_id']),  # TODO: remover após ajustar consultas do Analytics.

                    'user_creator': _user_creator,
                    'channel_type': _channel_type,
                    'channel_category': _channel_category,

                    'contributors': _contributors,
                    'pulses': _pulses,
                }

                docs.append(channel)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)

        return True

    def _query_channel_contributors(self, konquest_conn, channel_id):
        contributors = []

        rs = self.db.run_sql(konquest_conn, 'konquest/channel_contributors.sql', channel_id=channel_id)
        for row in rs:
            contributors.append({
                'relation_id': self.str_id(row['id']),
                'user_id': self.str_id(row['user_id']),
                'created_date': self.str_date(row['created_date']),
                'updated_date': self.str_date(row['updated_date']),
            })
        return contributors

    def _query_channel_pulses(self, konquest_conn, channel_id):
        pulses = []

        rs = self.db.run_sql(konquest_conn, 'konquest/channel_pulses.sql', channel_id=channel_id)
        for row in rs:
            channel = {
                'relation_id': self.str_id(row['id']),
                'id': self.str_id(row['channel_id']),
                'name': row['pulse_name'],
                'description': row['pulse_description'],
                'duration_time': row['duration_time'],
            }
            pulses.append(channel)
        return pulses
