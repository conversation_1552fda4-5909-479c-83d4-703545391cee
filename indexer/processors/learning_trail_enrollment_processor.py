import os
import typing
import math
from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class LearningTrailEnrollmentProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-analytics-learning-trail-enrollments-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'

    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.learning_trail_enrollment'

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            'properties': {
                'id': {'type': 'keyword'},
                'give_up': {'type': 'boolean'},
                'give_up_comment': {'type': 'text'},
                'status': {'type': 'keyword'},
                'required': {'type': 'boolean'},
                'points': {
                    'type': 'scaled_float',
                    'scaling_factor': 100,
                },
                'performance': {
                    'type': 'scaled_float',
                    'scaling_factor': 100,
                },
                'progress': {
                    'type': 'scaled_float',
                    'scaling_factor': 100,
                },

                'start_date': {'type': 'date'},
                'end_date': {'type': 'date'},
                'goal_date': {'type': 'date'},
                'created_date': { 'type': 'date' },
                'updated_date': { 'type': 'date' },

                'workspace_id': {'type': 'keyword'},

                'learning_trail': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'}
                            }
                        },
                    }
                },

                'user': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'keyword'},
                        'avatar': {'type': 'keyword'},
                        'name': {
                            'type': 'text',
                            'fields': {
                                "keyword": {'type': 'keyword'},
                            }
                        },
                    }
                },
            }
        }

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        with self.db.konquest_engine.connect() as konquest_conn:

            docs = []

            in_ids = ','.join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, 'konquest/learning_trail_enrollments_chunk.sql', enrollments_ids=in_ids)

            for i, row in enumerate(rs):
                enrollment_id = self.str_id(row['id'])
                self.log(f'[{enrollment_id}] -- Processing {i+1} of {len(updated_ids)}')

                # Many/One to One

                _user = {
                    'id': self.str_id(row['user_id']),
                    'name': row['user_name'],
                }

                _learning_trail = {
                    'id': self.str_id(row['learning_trail_id']),
                    'name': row['learning_trail_name'],
                }
                points = None if isinstance(row['points'], float) and math.isnan(row['points']) else row['points']
                performance = None if isinstance(row['performance'], float) and math.isnan(row['performance']) else row['performance']

                trail_enrollments = {
                    '_id': enrollment_id,  # elastic index id
                    'id': enrollment_id,
                    'status': row['status'],
                    'required': row['required'],
                    'points': points,
                    'performance': performance,
                    'progress': row['progress'],
                    'give_up': row['give_up'],
                    'give_up_comment': row['give_up_comment'],

                    'start_date': self.str_date(row['start_date']),
                    'end_date': self.str_date(row['end_date']),
                    'goal_date': self.str_date(row['goal_date']),
                    'created_date': self.str_date(row['created_date']),
                    'updated_date': self.str_date(row['updated_date']),

                    'workspace_id': self.str_id(row['workspace_id']),

                    'user': _user,
                    'learning_trail': _learning_trail,
                }

                docs.append(trail_enrollments)

            try:
                self.es.bulk_save(self.ELASTIC_INDEX, docs)
            except Exception as e:
                for err in e.errors:
                    self.log(err, error=True)
                raise

        return True
