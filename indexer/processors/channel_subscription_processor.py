import os
import typing
from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class ChannelSubscriptionProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-analytics-channel-subscription-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'

    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.channel_subscription'

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            'properties': {
                'id': {'type': 'keyword'},

                'status': {'type': 'keyword'},
                'active_subscription': {'type': 'boolean'},

                'subscription_date': { 'type': 'date' },
                'unsubscribe_date': { 'type': 'date' },
                'created_date': { 'type': 'date' },
                'updated_date': { 'type': 'date' },

                'user_id': {'type': 'keyword'},
                'channel_id': {'type': 'keyword'},
                'workspace_id': {'type': 'keyword'},
            }
        }

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        with self.db.konquest_engine.connect() as konquest_conn:

            docs = []

            in_ids = ','.join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, 'konquest/channel_subscriptions_chunk.sql', subscription_ids=in_ids)

            for i, row in enumerate(rs):
                subscription_id = self.str_id(row['id'])
                self.log(f'[{subscription_id}] -- Processing {i+1} of {len(updated_ids)}')

                subscription = {
                    '_id': subscription_id,  # elastic index id
                    'id': subscription_id,

                    'status': row['status'],
                    'active_subscription': row['active_subscription'],

                    'subscription_date': self.str_date(row['subscription_date']),
                    'unsubscribe_date': self.str_date(row['unsubscribe_date']),
                    'created_date': self.str_date(row['created_date']),
                    'updated_date': self.str_date(row['updated_date']),

                    'user_id': self.str_id(row['user_id']),
                    'channel_id': self.str_id(row['channel_id']),
                    'workspace_id': self.str_id(row['workspace_id']),
                }

                docs.append(subscription)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)

        return True
