import os
import typing

from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class CourseRatingsProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-analytics-course-ratings-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'

    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.mission_rating'

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            'properties': {
                'id': { 'type': 'keyword' },
                'user_id': {'type': 'keyword'},
                'course_id': {'type': 'keyword'},
                'workspace_id': {'type': 'keyword'},  # array
                'rating': {'type': 'integer'},
                'created_date': {'type': 'date'},
                'updated_date': {'type': 'date'},
            }
        }

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        with self.db.konquest_engine.connect() as konquest_conn:

            docs = []

            in_ids = ','.join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, 'konquest/mission_ratings_chunk.sql', ratings_ids=in_ids)

            for i, row in enumerate(rs):
                rating_id = self.str_id(row['id'])
                self.log(f'[{rating_id}] -- Processing {i+1} of {len(updated_ids)}')

                rating = {
                    '_id': rating_id,  # elastic index id
                    'id': rating_id,
                    'user_id': row['user_id'],
                    'course_id': row['mission_id'],
                    'rating': row['rating'],
                    'created_date': self.str_date(row['created_date']),
                    'updated_date': self.str_date(row['updated_date']),
                    'workspace_id': row['workspace_ids'],
                }
                docs.append(rating)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)

        return True
