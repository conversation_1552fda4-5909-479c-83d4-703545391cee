import os
import typing

from .. import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')

SERVICE_MAP = {
    'SMARTZAP': 'f9743ebc-c159-4dec-9600-cf1f9f0537b3'
}


class ServiceWorkspaceProcessor(AbstractProcessor):
    TOPIC_PREFIX = f'pg_myaccount_{ENV_SUFFIX}.public'

    def get_kafka_main_topic(self) -> str:
        return f'{self.TOPIC_PREFIX}.service_workspace'

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    # Pre-processing for main document
    def pre_process_batch(self, batch: typing.List[KeepsMessage]):

        # Processing for Smartzap
        smartzap_msgs = [msg for msg in batch if msg.size and msg.entity('service_id') == SERVICE_MAP['SMARTZAP']]

        delete_msgs = [msg for msg in smartzap_msgs if msg.op == 'd']
        update_msgs = [msg for msg in smartzap_msgs if msg.op != 'd']

        deleted_ids = self.extract_ids(delete_msgs, {
            self._kafka_main_topic: lambda msg: msg.entity('workspace_id'),
        })
        for id in deleted_ids:
            self._cache_deleted[id] = True

        updated_ids = self.extract_ids(update_msgs, {
            self._kafka_main_topic: lambda msg: msg.entity('workspace_id'),
        })
        updated_ids.difference_update(deleted_ids)
        updated_ids.difference_update(self._cache_deleted.keys())

        self.log(f'-- {len(batch)} messages, {len(updated_ids)} updated and {len(deleted_ids)} deleted.')

        if not updated_ids:
            self.log('-- No docs for processing...' )
            return True

        self.log('-- Processing...')
        return self.do_process_batch(batch, updated_ids, deleted_ids)

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str], deleted_ids: typing.Set[str]) -> bool:

        with self.db.myaccount_engine.connect() as myacc_conn,\
             self.db.smartzap_engine.connect() as smartzap_conn:

            # skip deleted_ids. TODO: implement Smartzap logical deletion

            in_ids = ','.join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(myacc_conn, 'smartzap/myaccount_workspaces_chunk.sql', workspaces_ids=in_ids)

            for i, row in enumerate(rs):
                workspace_id = self.str_id(row['id'])
                self.log(f'[{workspace_id}] -- Processing {i+1} of {len(updated_ids)}')

                values = self.db.format_query_values({
                    'id': self.str_id(row['id']),
                    'name': row['name'],
                    'logo_url': row['logo_url'],
                    'icon_url': row['icon_url'],
                })

                self.db.run_sql(smartzap_conn, 'smartzap/smarzap_workspace_save.sql', **values)

        return True
