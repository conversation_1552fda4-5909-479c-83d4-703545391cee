import os
import typing

from . import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class LearningTrailEnrollmentRelationsProcessor(AbstractProcessor):
    ELASTIC_INDEX = f'kafka-analytics-learning-trail-enrollments-{ENV_SUFFIX}'
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'
    USER_TOPIC = f'pg_myaccount_{ENV_SUFFIX}.public.user'

    def get_kafka_main_topic(self) -> str:
        return None

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        tables = [
            'learning_trail',
        ]
        topics = {f'{self.TOPIC_PREFIX}.{table}': None for table in tables}
        topics[self.USER_TOPIC] = None
        return topics

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    # Skip pre-processing for relations
    def pre_process_batch(self, batch: typing.List[KeepsMessage]):
        return self.do_process_batch(batch, None)

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:

        # ignore deleted and first time read related messages
        updated_msgs = [msg for msg in batch if msg.op not in ['d', 'r']]

        users = [user for user in updated_msgs if user.topic == self.USER_TOPIC]

        learning_trails = [trail for trail in updated_msgs if trail.topic == self.TOPIC_PREFIX + '.learning_trail']

        self.log(f'-- {len(batch)} messages, {len(users)} users, {len(learning_trails)} learning trails')

        for user in users:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'term': { 'user_creator.id': user.id}
                },
                'script': {
                    'lang': 'painless',
                    'source': 'ctx._source.user_creator.name = params["name"]',
                    'params': {
                        'name': user.entity("name")
                    }
                },
            })

        for trail in learning_trails:
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts='proceed', body={
                'query': {
                    'term': { 'learning_trail.id': trail.id}
                },
                'script': {
                    'lang': 'painless',
                    'source': 'ctx._source.learning_trail.name = params["name"]',
                    'params': {
                        'name': trail.entity("name")
                    }
                },
            })

        return True
