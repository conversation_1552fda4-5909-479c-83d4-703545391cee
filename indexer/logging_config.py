"""
Configuração simples do loguru para o Kafka Data Indexer.
"""

import os
import sys
from loguru import logger

# Configuração simples do loguru
logger.remove()
logger.add(
    sys.stdout,
    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name} | {message}",
    level=os.getenv('LOG_LEVEL', 'INFO'),
    colorize=True
)

def get_logger(name: str):
    """Retorna logger com nome específico."""
    return logger.bind(name=name)
