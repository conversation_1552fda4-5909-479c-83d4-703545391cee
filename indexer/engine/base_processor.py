import abc
import typing  # python 3.8<
import datetime
from .keeps_message import KeepsMessage
from ..logging_config import get_logger


# Base structure of a Processor
class BaseProcessor(abc.ABC):

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)

    # Run once when worker starts.
    def init(self):
        pass

    # Run once when worker terminates.
    def close(self):
        pass

    # Kafka topics associated with this processor. Must be implemented by subclasses.
    @abc.abstractmethod
    def get_kafka_topics(self) -> typing.List[str]:
        pass

    # Core processing for messages in batch. Must be implemented by subclasses.
    @abc.abstractmethod
    def process_batch(self, batch: typing.List[KeepsMessage]) -> bool:
        pass

    # Helper methods

    # Extract unique Ids using the specified extractors (per topic), if any, or the message ID if not specified.
    @staticmethod
    def extract_ids(batch: typing.List[KeepsMessage],
                    extractors: typing.Dict[str, typing.Callable[[KeepsMessage], str]] = {}
                    ) -> typing.Set[str]:
        ids = set()
        if extractors is None:
            extractors = {}
        for msg in batch:
            id = None
            if msg.size:
                extractor = extractors.get(msg.topic, lambda m: m.id)
                id = extractor(msg)
            if id:
                ids.add(id)
        return ids

    # Convert IDs to string or None
    @staticmethod
    def str_id(id) -> str:
        if id is not None:
            return str(id)
        return None

    # Convert datetimes to string or None
    @staticmethod
    def str_date(dt: datetime.datetime):
        return dt.isoformat() if dt else None

    # Logs messages with loguru
    def log(self, msg):
        """Log a message using loguru."""
        self.logger.info(msg)
