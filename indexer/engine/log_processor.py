import typing  # python 3.8<

from .base_processor import BaseProcessor
from .keeps_message import KeepsMessage
from ..logging_config import get_logger


class LogProcessor(BaseProcessor):
    def __init__(self, topics: typing.List[str] = [], logMessages: bool = True) -> None:
        self.topics = topics
        self.logMessages = logMessages
        self.logger = get_logger("LogProcessor")

    def get_kafka_topics(self) -> typing.List[str]:
        return self.topics

    def process_batch(self, batch: typing.List[KeepsMessage]) -> bool:
        self.logger.info('-----------------------')
        self.logger.info(f'batch size: {len(batch)}')

        if self.logMessages:
            for i, msg in enumerate(batch, start=1):
                self.logger.info("")
                self.logger.info(f'message: {i}')
                msg.log()

        self.logger.info("")
        return True
