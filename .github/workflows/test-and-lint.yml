name: <PERSON><PERSON> (Python)

on:
  workflow_call:

jobs:
  coverage:
    name: Coverage and <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - name: Update System Dependencies
        run: mkdir -p .reports
      
      - uses: chartboost/ruff-action@v1
        name: Running Code Conventions
        with:
          args: check -v --output-format=pylint --output-file=.reports/output_flake.txt
      
      - uses: actions/upload-artifact@v4
        with:
          name: reports
          path: .reports  
          include-hidden-files: true