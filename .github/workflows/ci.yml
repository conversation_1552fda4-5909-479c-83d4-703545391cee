name: <PERSON><PERSON><PERSON> (CI)
on:
  workflow_dispatch:
  pull_request:
    branches:
      - main
      - develop
    types: [opened, synchronize, reopened]
jobs:
  test-and-lint:
    name: <PERSON><PERSON>
    uses: ./.github/workflows/test-and-lint.yml
    
  sonar:
    name: Quality Analysis
    uses: ./.github/workflows/sonar.yml
    needs: [test-and-lint]
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}