services: 

  kafka-data-indexer:
    build: .
    container_name: konquest-data-sync
    command: "python -u ./app.py"
    env_file: "./.env"
    networks:
      - keeps-net

  regulatory-compliance-data-sync:
    build: .
    container_name: konquest-data-sync
    command: "python -u ./app-regulatory-compliance-data-sync.py"
    env_file: "./.env"
    networks:
      - keeps-net

  konquest-data-sync:
    build: .
    container_name: konquest-data-sync
    command: "python -u ./app-konquest-data-sync.py"
    env_file: "./.env"
    networks:
      - keeps-net
      
  smartzap-data-sync:
    build: .
    container_name: smartzap-data-sync
    command: "python -u ./app-smartzap-data-sync.py"
    env_file: "./.env"
    networks:
      - keeps-net

  elastic:
    image: elasticsearch:7.14.2
    container_name: elastic
    ports:
      - 9200:9200
      - 9300:9300
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=senhaSuperSegura
    networks:
      - keeps-net

  kibana:
    image: kibana:7.14.2
    container_name: kibana
    ports:
      - 5601:5601
    depends_on:
      - elastic
    environment:
      ELASTICSEARCH_HOSTS: http://elastic:9200
    networks:
      - keeps-net

  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.0
    container_name: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
    ports:
      - "2181:2181"
    networks:
      - keeps-net

  kafka:
    image: confluentinc/cp-kafka:7.5.0
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    networks:
      - keeps-net

  control-center:
    image: confluentinc/cp-enterprise-control-center:7.5.0
    container_name: kafka-control-center
    depends_on:
      - kafka
    ports:
      - "9021:9021"
    environment:
      CONTROL_CENTER_BOOTSTRAP_SERVERS: kafka:9092
      CONTROL_CENTER_ZOOKEEPER_CONNECT: zookeeper:2181
      CONTROL_CENTER_REPLICATION_FACTOR: 1
      CONTROL_CENTER_CONNECT_CLUSTER: kafka:9092
      CONFLUENT_METRICS_ENABLE: 'false'
    networks:
      - keeps-net

networks:
  keeps-net: {}